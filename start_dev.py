#!/usr/bin/env python3
"""
Quick start script for DudeAI AI Agent System local development
Starts both backend and frontend servers
"""
import subprocess
import sys
import time
import threading
import os
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output"""
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    OKBLUE = '\033[94m'

def print_colored(message, color=Colors.OKBLUE):
    """Print colored message"""
    print(f"{color}{message}{Colors.ENDC}")

def print_success(message):
    """Print success message"""
    print_colored(f"✓ {message}", Colors.OKGREEN)

def print_error(message):
    """Print error message"""
    print_colored(f"✗ {message}", Colors.FAIL)

def print_warning(message):
    """Print warning message"""
    print_colored(f"⚠ {message}", Colors.WARNING)

def check_prerequisites():
    """Check if setup is complete"""
    print_colored("Checking prerequisites...", Colors.BOLD)
    
    # Check backend virtual environment
    backend_venv = Path("backend/venv")
    if not backend_venv.exists():
        print_error("Backend virtual environment not found")
        print_colored("Run: cd backend && python -m venv venv && venv\\Scripts\\pip install -r requirements.txt")
        return False
    
    # Check backend .env file
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        print_warning("Backend .env file not found")
        print_colored("Copying .env.example to .env...")
        try:
            import shutil
            shutil.copy("backend/.env.example", "backend/.env")
            print_success("Created backend/.env from template")
            print_warning("Please edit backend/.env with your MongoDB URL and OpenAI API key")
        except Exception as e:
            print_error(f"Failed to create .env file: {e}")
            return False
    
    # Check frontend node_modules
    frontend_modules = Path("frontend/node_modules")
    if not frontend_modules.exists():
        print_error("Frontend dependencies not installed")
        print_colored("Run: cd frontend && npm install")
        return False
    
    print_success("Prerequisites check passed")
    return True

def start_backend():
    """Start the backend server"""
    print_colored("Starting backend server...", Colors.BOLD)
    
    backend_dir = Path("backend")
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    try:
        # Start backend server
        process = subprocess.Popen(
            [python_cmd, "server.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print_success("Backend server starting...")
        print_colored("Backend will be available at: http://localhost:8000", Colors.OKBLUE)
        print_colored("API documentation at: http://localhost:8000/docs", Colors.OKBLUE)
        
        # Monitor backend output
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print_colored(f"[Backend] {line.strip()}", Colors.OKGREEN)
                if "Application startup complete" in line or "Uvicorn running" in line:
                    print_success("Backend server is ready!")
                    break
        
        return process
        
    except Exception as e:
        print_error(f"Failed to start backend: {e}")
        return None

def start_frontend():
    """Start the frontend server"""
    print_colored("Starting frontend server...", Colors.BOLD)
    
    frontend_dir = Path("frontend")
    
    try:
        # Start frontend server
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print_success("Frontend server starting...")
        print_colored("Frontend will be available at: http://localhost:3000", Colors.OKBLUE)
        
        # Monitor frontend output
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print_colored(f"[Frontend] {line.strip()}", Colors.OKBLUE)
                if "webpack compiled" in line.lower() or "compiled successfully" in line.lower():
                    print_success("Frontend server is ready!")
                    break
                elif "something is already running on port 3000" in line.lower():
                    print_warning("Port 3000 is already in use")
                    break
        
        return process
        
    except Exception as e:
        print_error(f"Failed to start frontend: {e}")
        return None

def main():
    """Main function"""
    print_colored("""
╔══════════════════════════════════════════════════════════════╗
║                    DudeAI Development Server                 ║
║                      Quick Start Script                     ║
╚══════════════════════════════════════════════════════════════╝
    """, Colors.BOLD)
    
    # Check prerequisites
    if not check_prerequisites():
        print_error("Prerequisites not met. Please run setup first.")
        sys.exit(1)
    
    print_colored("\nStarting DudeAI AI Agent System...", Colors.BOLD)
    
    backend_process = None
    frontend_process = None
    
    try:
        # Start backend first
        backend_process = start_backend()
        if not backend_process:
            print_error("Failed to start backend server")
            sys.exit(1)
        
        # Wait a moment for backend to fully start
        print_colored("Waiting for backend to initialize...", Colors.WARNING)
        time.sleep(3)
        
        # Start frontend
        frontend_process = start_frontend()
        if not frontend_process:
            print_error("Failed to start frontend server")
            if backend_process:
                backend_process.terminate()
            sys.exit(1)
        
        print_colored("\n" + "="*60, Colors.BOLD)
        print_success("DudeAI AI Agent System is running!")
        print_colored("="*60, Colors.BOLD)
        print_colored("Frontend: http://localhost:3000", Colors.OKGREEN)
        print_colored("Backend:  http://localhost:8000", Colors.OKGREEN)
        print_colored("API Docs: http://localhost:8000/docs", Colors.OKGREEN)
        print_colored("="*60, Colors.BOLD)
        print_colored("\nPress Ctrl+C to stop both servers", Colors.WARNING)
        
        # Wait for user to stop
        try:
            while True:
                time.sleep(1)
                # Check if processes are still running
                if backend_process.poll() is not None:
                    print_error("Backend process stopped unexpectedly")
                    break
                if frontend_process.poll() is not None:
                    print_error("Frontend process stopped unexpectedly")
                    break
        except KeyboardInterrupt:
            print_colored("\nShutting down servers...", Colors.WARNING)
    
    except Exception as e:
        print_error(f"Error during startup: {e}")
    
    finally:
        # Clean up processes
        if backend_process:
            print_colored("Stopping backend server...", Colors.WARNING)
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
        
        if frontend_process:
            print_colored("Stopping frontend server...", Colors.WARNING)
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()
        
        print_success("Servers stopped. Goodbye!")

if __name__ == "__main__":
    main()
