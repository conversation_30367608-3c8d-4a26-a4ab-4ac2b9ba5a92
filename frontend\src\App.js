import React, { useState, useEffect } from "react";
import "./App.css";
import axios from "axios";

// API Configuration from environment variables
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';
const API = API_URL;

// Development configuration
const IS_DEVELOPMENT = process.env.REACT_APP_ENVIRONMENT === 'development';
const ENABLE_LOGGING = process.env.REACT_APP_ENABLE_LOGGING === 'true';
const API_TIMEOUT = parseInt(process.env.REACT_APP_API_TIMEOUT) || 30000;
const ITEMS_PER_PAGE = parseInt(process.env.REACT_APP_ITEMS_PER_PAGE) || 20;
const SEARCH_DEBOUNCE_MS = parseInt(process.env.REACT_APP_SEARCH_DEBOUNCE_MS) || 500;

// Logging utility
const log = (...args) => {
  if (IS_DEVELOPMENT && ENABLE_LOGGING) {
    console.log('[DudeAI]', ...args);
  }
};

// Tool Categories
const TOOL_CATEGORIES = [
  "Text Generation",
  "Image Generation", 
  "Video Generation",
  "Audio Generation",
  "Code Generation",
  "Data Analysis",
  "Chatbots",
  "Automation",
  "Design",
  "Marketing",
  "Productivity",
  "Education",
  "Research",
  "Content Creation",
  "Translation",
  "Summarization",
  "Other"
];

// Tag Types
const TAG_TYPES = [
  { type: "Trending", color: "#ef4444" },
  { type: "New", color: "#10b981" },
  { type: "Premium", color: "#8b5cf6" },
  { type: "Featured", color: "#f59e0b" },
  { type: "Popular", color: "#3b82f6" }
];

// Individual Components
const Header = () => (
  <header className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-6 px-4">
    <div className="max-w-7xl mx-auto">
      <h1 className="text-4xl font-bold mb-2">🤖 DudeAI</h1>
      <p className="text-xl opacity-90">AI Agent System - Comprehensive AI Tool Discovery & Analysis</p>
    </div>
  </header>
);

const StatsCard = ({ stats }) => (
  <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
    <h2 className="text-2xl font-bold mb-4 text-gray-800">System Statistics</h2>
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="text-2xl font-bold text-blue-600">{stats.total_tools}</div>
        <div className="text-sm text-gray-600">Total Tools</div>
      </div>
      <div className="bg-green-50 p-4 rounded-lg">
        <div className="text-2xl font-bold text-green-600">{stats.completed_tools}</div>
        <div className="text-sm text-gray-600">AI Generated</div>
      </div>
      <div className="bg-purple-50 p-4 rounded-lg">
        <div className="text-2xl font-bold text-purple-600">{stats.published_tools}</div>
        <div className="text-sm text-gray-600">Published</div>
      </div>
      <div className="bg-yellow-50 p-4 rounded-lg">
        <div className="text-2xl font-bold text-yellow-600">{stats.automation_rate}%</div>
        <div className="text-sm text-gray-600">Automation Rate</div>
      </div>
      <div className="bg-indigo-50 p-4 rounded-lg">
        <div className="text-2xl font-bold text-indigo-600">{stats.average_quality_score}/100</div>
        <div className="text-sm text-gray-600">Avg Quality</div>
      </div>
    </div>
  </div>
);

const AddToolForm = ({ onToolAdded, isLoading }) => {
  const [formData, setFormData] = useState({
    name: '',
    website: '',
    category: '',
    short_description: '',
    company: '',
    subcategory: '',
    logo_url: '',
    screenshots: [],
    social_links: {
      twitter: '',
      linkedin: '',
      github: '',
      facebook: '',
      youtube: ''
    },
    tags: []
  });

  const [currentScreenshot, setCurrentScreenshot] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const submitData = {
        ...formData,
        screenshots: formData.screenshots.filter(url => url.trim()),
        tags: selectedTags,
        social_links: Object.keys(formData.social_links).reduce((acc, key) => {
          if (formData.social_links[key].trim()) {
            acc[key] = formData.social_links[key];
          }
          return acc;
        }, {})
      };

      const response = await axios.post(`${API}/tools`, submitData);
      onToolAdded(response.data);
      setFormData({ 
        name: '', website: '', category: '', short_description: '', company: '', 
        subcategory: '', logo_url: '', screenshots: [], 
        social_links: { twitter: '', linkedin: '', github: '', facebook: '', youtube: '' },
        tags: []
      });
      setSelectedTags([]);
      setCurrentScreenshot('');
    } catch (error) {
      console.error('Error adding tool:', error);
      alert('Error adding tool. Please try again.');
    }
  };

  const addScreenshot = () => {
    if (currentScreenshot.trim() && !formData.screenshots.includes(currentScreenshot.trim())) {
      setFormData({
        ...formData,
        screenshots: [...formData.screenshots, currentScreenshot.trim()]
      });
      setCurrentScreenshot('');
    }
  };

  const removeScreenshot = (index) => {
    setFormData({
      ...formData,
      screenshots: formData.screenshots.filter((_, i) => i !== index)
    });
  };

  const toggleTag = (tag) => {
    if (selectedTags.some(t => t.type === tag.type)) {
      setSelectedTags(selectedTags.filter(t => t.type !== tag.type));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">➕ Add New AI Tool</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Basic Information</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tool Name *</label>
              <input
                type="text"
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., ChatGPT, DALL-E, Midjourney"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Website URL</label>
              <input
                type="url"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="https://example.com"
                value={formData.website}
                onChange={(e) => setFormData({...formData, website: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
              <input
                type="text"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., OpenAI, Anthropic, Google"
                value={formData.company}
                onChange={(e) => setFormData({...formData, company: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Logo URL</label>
              <input
                type="url"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="https://example.com/logo.png"
                value={formData.logo_url}
                onChange={(e) => setFormData({...formData, logo_url: e.target.value})}
              />
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Categorization</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category *</label>
              <select
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
              >
                <option value="">Select a category</option>
                {TOOL_CATEGORIES.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Subcategory</label>
              <input
                type="text"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Conversational AI, Image Editing"
                value={formData.subcategory}
                onChange={(e) => setFormData({...formData, subcategory: e.target.value})}
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Description</h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Short Description *</label>
            <textarea
              required
              rows="3"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Brief description of what this AI tool does... (150 characters max)"
              maxLength="150"
              value={formData.short_description}
              onChange={(e) => setFormData({...formData, short_description: e.target.value})}
            />
            <div className="text-xs text-gray-500 mt-1">
              {formData.short_description.length}/150 characters
            </div>
          </div>
        </div>

        {/* Screenshots */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Screenshots</h3>
          <div className="flex gap-2 mb-3">
            <input
              type="url"
              className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Screenshot URL"
              value={currentScreenshot}
              onChange={(e) => setCurrentScreenshot(e.target.value)}
            />
            <button
              type="button"
              onClick={addScreenshot}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Add
            </button>
          </div>
          {formData.screenshots.length > 0 && (
            <div className="space-y-2">
              {formData.screenshots.map((screenshot, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <span className="text-sm text-gray-600 truncate">{screenshot}</span>
                  <button
                    type="button"
                    onClick={() => removeScreenshot(index)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Social Links */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Social Media Links</h3>
          <div className="grid md:grid-cols-2 gap-4">
            {Object.keys(formData.social_links).map(platform => (
              <div key={platform}>
                <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                  {platform}
                </label>
                <input
                  type="url"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={`https://${platform}.com/...`}
                  value={formData.social_links[platform]}
                  onChange={(e) => setFormData({
                    ...formData,
                    social_links: { ...formData.social_links, [platform]: e.target.value }
                  })}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div className="border-b pb-4">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {TAG_TYPES.map(tag => (
              <button
                key={tag.type}
                type="button"
                onClick={() => toggleTag(tag)}
                className={`px-3 py-1 rounded-full text-sm font-medium border-2 transition-all ${
                  selectedTags.some(t => t.type === tag.type)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                }`}
                style={selectedTags.some(t => t.type === tag.type) ? { backgroundColor: tag.color, borderColor: tag.color } : {}}
              >
                {tag.type}
              </button>
            ))}
          </div>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 disabled:opacity-50"
        >
          {isLoading ? 'Adding Tool...' : 'Add AI Tool'}
        </button>
      </form>
    </div>
  );
};

const ToolCard = ({ tool, onGenerateContent }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFullContent, setShowFullContent] = useState(false);

  const handleGenerateContent = async () => {
    setIsGenerating(true);
    try {
      await onGenerateContent(tool.id);
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return '✅';
      case 'processing': return '⏳';
      case 'failed': return '❌';
      default: return '⏸️';
    }
  };

  const getPricingBadge = (pricing) => {
    if (!pricing || typeof pricing !== 'object') return null;
    const colors = {
      'free': 'bg-green-100 text-green-800',
      'freemium': 'bg-blue-100 text-blue-800',
      'paid': 'bg-purple-100 text-purple-800',
      'subscription': 'bg-orange-100 text-orange-800'
    };
    return (
      <span className={`inline-block px-2 py-1 text-xs rounded-full ${colors[pricing.type] || 'bg-gray-100 text-gray-800'}`}>
        {pricing.type || 'Unknown'}
        {pricing.starting_price && pricing.starting_price > 0 && pricing.billing_cycle && 
          ` $${pricing.starting_price}/${pricing.billing_cycle}`}
      </span>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-start space-x-3">
          {tool.logo_url && (
            <img
              src={tool.logo_url}
              alt={`${tool.name} logo`}
              className="w-12 h-12 rounded-lg object-cover"
              onError={(e) => { e.target.style.display = 'none'; }}
            />
          )}
          <div>
            <h3 className="text-xl font-bold text-gray-800 mb-1">{tool.name}</h3>
            {tool.company && (
              <p className="text-sm text-gray-600 mb-2">by {tool.company}</p>
            )}
            <div className="flex flex-wrap gap-1">
              <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {tool.category}
              </span>
              {tool.subcategory && (
                <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                  {tool.subcategory}
                </span>
              )}
              {tool.pricing && getPricingBadge(tool.pricing)}
            </div>
          </div>
        </div>
        <div className="text-right">
          <span className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(tool.ai_generation_status || tool.generation_status)}`}>
            {getStatusIcon(tool.ai_generation_status || tool.generation_status)} {tool.ai_generation_status || tool.generation_status}
          </span>
          {(tool.content_quality_score || tool.quality_score) && (
            <div className="text-sm text-gray-600 mt-1">
              Quality: {Math.round(tool.content_quality_score || tool.quality_score)}/100
            </div>
          )}
          {tool.is_verified && (
            <div className="text-xs text-green-600 mt-1">✓ Verified</div>
          )}
        </div>
      </div>

      {/* Tags */}
      {tool.tags && Array.isArray(tool.tags) && tool.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {tool.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-block px-2 py-1 text-xs rounded-full text-white"
              style={{ backgroundColor: (tag && tag.color) || '#6b7280' }}
            >
              {(tag && tag.type) || 'Unknown'}
            </span>
          ))}
        </div>
      )}

      {/* Description */}
      <p className="text-gray-600 mb-4">{tool.short_description || tool.description}</p>
      
      {/* AI Generated Content */}
      {(tool.description || tool.ai_description) && (
        <div className="border-t pt-4 mt-4">
          <h4 className="font-semibold text-gray-800 mb-2">🤖 AI Generated Content:</h4>
          
          {/* Main Description */}
          {(tool.description || tool.ai_description) && (
            <div className="mb-3">
              <p className="text-gray-700 text-sm">
                {showFullContent 
                  ? (tool.description || tool.ai_description)
                  : `${(tool.description || tool.ai_description).substring(0, 200)}...`
                }
              </p>
              {(tool.description || tool.ai_description).length > 200 && (
                <button
                  onClick={() => setShowFullContent(!showFullContent)}
                  className="text-blue-600 text-xs hover:text-blue-800 mt-1"
                >
                  {showFullContent ? 'Show less' : 'Show more'}
                </button>
              )}
            </div>
          )}
          
          {/* Features */}
          {(tool.features || tool.ai_features) && (tool.features || tool.ai_features).length > 0 && (
            <div className="mb-3">
              <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                {(tool.features || tool.ai_features).slice(0, 3).map((feature, idx) => (
                  <li key={idx}>• {feature}</li>
                ))}
                {(tool.features || tool.ai_features).length > 3 && (
                  <li className="text-blue-600">... and {(tool.features || tool.ai_features).length - 3} more features</li>
                )}
              </ul>
            </div>
          )}

          {/* Pricing */}
          {(tool.pricing || tool.ai_pricing) && (
            <div className="mb-3">
              <h5 className="font-medium text-gray-700 mb-1">Pricing:</h5>
              <p className="text-sm text-gray-600">
                {tool.pricing && typeof tool.pricing === 'object' 
                  ? (tool.pricing.description || `${tool.pricing.type || 'Unknown'} - Starting at $${tool.pricing.starting_price || 0}`)
                  : (tool.ai_pricing || 'Pricing information not available')
                }
              </p>
            </div>
          )}

          {/* Pros and Cons */}
          <div className="grid md:grid-cols-2 gap-4 mt-3">
            {((tool.pros_and_cons && tool.pros_and_cons.pros) || tool.ai_pros) && 
             ((tool.pros_and_cons && tool.pros_and_cons.pros && tool.pros_and_cons.pros.length > 0) || 
              (tool.ai_pros && tool.ai_pros.length > 0)) && (
              <div>
                <h5 className="font-medium text-green-700 mb-1">✅ Pros:</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  {((tool.pros_and_cons && tool.pros_and_cons.pros) || tool.ai_pros || []).slice(0, 2).map((pro, idx) => (
                    <li key={idx}>• {pro}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {((tool.pros_and_cons && tool.pros_and_cons.cons) || tool.ai_cons) && 
             ((tool.pros_and_cons && tool.pros_and_cons.cons && tool.pros_and_cons.cons.length > 0) || 
              (tool.ai_cons && tool.ai_cons.length > 0)) && (
              <div>
                <h5 className="font-medium text-red-700 mb-1">⚠️ Cons:</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  {((tool.pros_and_cons && tool.pros_and_cons.cons) || tool.ai_cons || []).slice(0, 2).map((con, idx) => (
                    <li key={idx}>• {con}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* FAQs */}
          {tool.faqs && Array.isArray(tool.faqs) && tool.faqs.length > 0 && (
            <div className="mt-3">
              <h5 className="font-medium text-gray-700 mb-2">❓ FAQs:</h5>
              <div className="space-y-2">
                {tool.faqs.slice(0, 2).map((faq, idx) => (
                  <div key={idx} className="bg-gray-50 p-2 rounded">
                    <p className="text-sm font-medium text-gray-800">{faq.question || 'No question'}</p>
                    <p className="text-xs text-gray-600 mt-1">{faq.answer || 'No answer'}</p>
                  </div>
                ))}
                {tool.faqs.length > 2 && (
                  <p className="text-xs text-blue-600">... and {tool.faqs.length - 2} more FAQs</p>
                )}
              </div>
            </div>
          )}

          {/* Haiku */}
          {tool.haiku && (
            <div className="mt-3 bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
              <h5 className="font-medium text-purple-700 mb-1">🎋 AI Haiku:</h5>
              <p className="text-sm text-gray-700 italic whitespace-pre-line">{tool.haiku}</p>
            </div>
          )}
        </div>
      )}

      {/* Social Links */}
      {tool.social_links && typeof tool.social_links === 'object' && 
       Object.keys(tool.social_links).some(key => tool.social_links[key]) && (
        <div className="border-t pt-3 mt-3">
          <h5 className="font-medium text-gray-700 mb-2">🔗 Social Links:</h5>
          <div className="flex flex-wrap gap-2">
            {Object.entries(tool.social_links).map(([platform, url]) => 
              url && typeof url === 'string' && url.trim() && (
                <a
                  key={platform}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded capitalize"
                >
                  {platform}
                </a>
              )
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center mt-4 pt-4 border-t">
        <div className="flex gap-2">
          {tool.website && (
            <a
              href={tool.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              🔗 Website
            </a>
          )}
          {tool.link && (
            <a
              href={tool.link}
              className="text-green-600 hover:text-green-800 text-sm font-medium"
            >
              📄 Full Profile
            </a>
          )}
        </div>
        
        {(tool.ai_generation_status || tool.generation_status) !== 'completed' && (
          <button
            onClick={handleGenerateContent}
            disabled={isGenerating || (tool.ai_generation_status || tool.generation_status) === 'processing'}
            className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-green-600 hover:to-blue-600 transition duration-200 disabled:opacity-50"
          >
            {isGenerating || (tool.ai_generation_status || tool.generation_status) === 'processing' ? 
              '🔄 Generating...' : '🚀 Generate AI Content'}
          </button>
        )}
      </div>
    </div>
  );
};

const FilterBar = ({ categories, selectedCategory, onCategoryChange, searchTerm, onSearchChange, selectedStatus, onStatusChange }) => (
  <div className="bg-white rounded-lg shadow-lg p-4 mb-6">
    <div className="flex flex-col md:flex-row gap-4">
      <div className="flex-1">
        <input
          type="text"
          placeholder="🔍 Search AI tools..."
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      <div className="md:w-48">
        <select
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={selectedCategory}
          onChange={(e) => onCategoryChange(e.target.value)}
        >
          <option value="">All Categories</option>
          {categories.map(cat => (
            <option key={cat} value={cat}>{cat}</option>
          ))}
        </select>
      </div>
      <div className="md:w-48">
        <select
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={selectedStatus}
          onChange={(e) => onStatusChange(e.target.value)}
        >
          <option value="">All Status</option>
          <option value="completed">AI Generated</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="failed">Failed</option>
        </select>
      </div>
    </div>
  </div>
);

// Main App Component
function App() {
  const [tools, setTools] = useState([]);
  const [stats, setStats] = useState({
    total_tools: 0,
    completed_tools: 0,
    published_tools: 0,
    automation_rate: 0,
    average_quality_score: 0
  });
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('tools'); // 'tools' or 'add'

  // Fetch data
  const fetchTools = async () => {
    try {
      let url = `${API}/tools?limit=100`;
      const params = new URLSearchParams();
      
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedStatus) params.append('status', selectedStatus);
      if (searchTerm) params.append('search', searchTerm);
      
      if (params.toString()) {
        url += `&${params.toString()}`;
      }
      
      const response = await axios.get(url);
      setTools(response.data);
    } catch (error) {
      console.error('Error fetching tools:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get(`${API}/stats`);
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API}/categories`);
      setCategories(response.data.categories);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  useEffect(() => {
    fetchTools();
    fetchStats();
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchTools();
  }, [selectedCategory, selectedStatus, searchTerm]);

  // Event handlers
  const handleToolAdded = (newTool) => {
    setTools([newTool, ...tools]);
    fetchStats();
    setActiveTab('tools');
    alert('🎉 Tool added successfully! Click "Generate AI Content" to create comprehensive content.');
  };

  const handleGenerateContent = async (toolId) => {
    try {
      const response = await axios.post(`${API}/tools/${toolId}/generate-content`);
      if (response.data.success) {
        fetchTools();
        fetchStats();
        alert(`✅ AI content generated successfully! Quality Score: ${Math.round(response.data.quality_score)}/100`);
      }
    } catch (error) {
      console.error('Error generating content:', error);
      alert('❌ Error generating content. Please try again.');
    }
  };

  // Filter tools
  const filteredTools = tools.filter(tool => {
    const matchesCategory = !selectedCategory || tool.category === selectedCategory;
    const matchesStatus = !selectedStatus || (tool.ai_generation_status || tool.generation_status) === selectedStatus;
    const matchesSearch = !searchTerm || 
      tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tool.short_description && tool.short_description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tool.description && tool.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tool.company && tool.company.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 py-8">
        <StatsCard stats={stats} />
        
        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('tools')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'tools' 
                    ? 'border-blue-500 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                🔍 AI Tools Directory ({filteredTools.length})
              </button>
              <button
                onClick={() => setActiveTab('add')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'add' 
                    ? 'border-blue-500 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                ➕ Add New Tool
              </button>
            </nav>
          </div>
        </div>

        {activeTab === 'add' && (
          <AddToolForm onToolAdded={handleToolAdded} isLoading={isLoading} />
        )}

        {activeTab === 'tools' && (
          <>
            <FilterBar
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
            />

            {filteredTools.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🤖</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">No AI tools found</h3>
                <p className="text-gray-500 mb-4">
                  {tools.length === 0 ? 
                    "Get started by adding your first AI tool!" : 
                    "Try adjusting your search or filter criteria."
                  }
                </p>
                {tools.length === 0 && (
                  <button
                    onClick={() => setActiveTab('add')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition duration-200"
                  >
                    Add First Tool
                  </button>
                )}
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTools.map(tool => (
                  <ToolCard
                    key={tool.id}
                    tool={tool}
                    onGenerateContent={handleGenerateContent}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-12">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p className="text-lg font-semibold mb-2">🚀 DudeAI AI Agent System</p>
          <p className="text-gray-400">Comprehensive AI tool discovery and content generation platform</p>
          <div className="mt-4 text-sm text-gray-500">
            Enhanced with SEO metadata, social links, tags, FAQs, and comprehensive content structure
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Powered by OpenAI GPT-4o • Built with FastAPI & React
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;