"""
Security tests for DudeAI AI Agent System
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
import uuid

from server import app
from security import SecurityMiddleware, InputValidator
from config import settings

client = TestClient(app)

class TestSecurityMiddleware:
    """Test security middleware functions"""
    
    def test_validate_uuid_valid(self):
        """Test UUID validation with valid UUID"""
        valid_uuid = str(uuid.uuid4())
        assert SecurityMiddleware.validate_uuid(valid_uuid) == True
    
    def test_validate_uuid_invalid(self):
        """Test UUID validation with invalid UUID"""
        invalid_uuid = "not-a-uuid"
        assert SecurityMiddleware.validate_uuid(invalid_uuid) == False
    
    def test_sanitize_string_normal(self):
        """Test string sanitization with normal input"""
        input_text = "This is a normal string"
        result = SecurityMiddleware.sanitize_string(input_text)
        assert result == "This is a normal string"
    
    def test_sanitize_string_dangerous_chars(self):
        """Test string sanitization removes dangerous characters"""
        input_text = "This has <script>alert('xss')</script> dangerous content"
        result = SecurityMiddleware.sanitize_string(input_text)
        assert "<script>" not in result
        assert "alert(xss)" in result
    
    def test_sanitize_string_length_limit(self):
        """Test string sanitization respects length limits"""
        long_text = "a" * 2000
        result = SecurityMiddleware.sanitize_string(long_text, 100)
        assert len(result) == 100
    
    def test_validate_url_valid(self):
        """Test URL validation with valid URLs"""
        valid_urls = [
            "https://example.com",
            "http://localhost:3000",
            "https://api.example.com/path?param=value"
        ]
        for url in valid_urls:
            assert SecurityMiddleware.validate_url(url) == True
    
    def test_validate_url_invalid(self):
        """Test URL validation with invalid URLs"""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",
            "javascript:alert('xss')"
        ]
        for url in invalid_urls:
            assert SecurityMiddleware.validate_url(url) == False
    
    def test_validate_email_valid(self):
        """Test email validation with valid emails"""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>"
        ]
        for email in valid_emails:
            assert SecurityMiddleware.validate_email(email) == True
    
    def test_validate_email_invalid(self):
        """Test email validation with invalid emails"""
        invalid_emails = [
            "not-an-email",
            "@domain.com",
            "user@"
        ]
        for email in invalid_emails:
            assert SecurityMiddleware.validate_email(email) == False

class TestInputValidator:
    """Test input validation functions"""
    
    def test_validate_tool_name_valid(self):
        """Test tool name validation with valid names"""
        valid_name = "Test Tool"
        result = InputValidator.validate_tool_name(valid_name)
        assert result == "Test Tool"
    
    def test_validate_tool_name_empty(self):
        """Test tool name validation with empty name"""
        with pytest.raises(Exception):
            InputValidator.validate_tool_name("")
    
    def test_validate_tool_name_too_short(self):
        """Test tool name validation with too short name"""
        with pytest.raises(Exception):
            InputValidator.validate_tool_name("A")
    
    def test_validate_category_valid(self):
        """Test category validation with valid category"""
        valid_category = "Text Generation"
        result = InputValidator.validate_category(valid_category)
        assert result == "Text Generation"
    
    def test_validate_category_invalid(self):
        """Test category validation with invalid category"""
        with pytest.raises(Exception):
            InputValidator.validate_category("Invalid Category")
    
    def test_validate_url_field_valid(self):
        """Test URL field validation with valid URL"""
        valid_url = "https://example.com"
        result = InputValidator.validate_url_field(valid_url, "website")
        assert result == "https://example.com"
    
    def test_validate_url_field_invalid(self):
        """Test URL field validation with invalid URL"""
        with pytest.raises(Exception):
            InputValidator.validate_url_field("not-a-url", "website")
    
    def test_validate_screenshots_valid(self):
        """Test screenshot validation with valid URLs"""
        valid_screenshots = [
            "https://example.com/image1.png",
            "https://example.com/image2.jpg"
        ]
        result = InputValidator.validate_screenshots(valid_screenshots)
        assert len(result) == 2
    
    def test_validate_screenshots_too_many(self):
        """Test screenshot validation with too many URLs"""
        too_many_screenshots = ["https://example.com/image.png"] * 15
        with pytest.raises(Exception):
            InputValidator.validate_screenshots(too_many_screenshots)

class TestAPIEndpoints:
    """Test API endpoint security"""
    
    @patch('server.db_manager')
    def test_health_endpoint_rate_limit(self, mock_db):
        """Test health endpoint respects rate limits"""
        mock_db.health_check.return_value = {"status": "healthy"}
        
        # Make multiple requests to test rate limiting
        responses = []
        for _ in range(15):  # Exceed the 10/minute limit
            response = client.get("/api/health")
            responses.append(response.status_code)
        
        # Should have some rate limited responses
        assert 429 in responses
    
    def test_invalid_tool_id_format(self):
        """Test endpoints reject invalid tool ID formats"""
        invalid_id = "not-a-uuid"
        response = client.get(f"/api/tools/{invalid_id}")
        assert response.status_code == 400
        assert "Invalid tool ID format" in response.json()["detail"]
    
    def test_sql_injection_attempt(self):
        """Test endpoints are protected against injection attempts"""
        injection_attempt = "'; DROP TABLE ai_tools; --"
        response = client.get(f"/api/tools/slug/{injection_attempt}")
        # Should sanitize the input and return 404 (not found) rather than error
        assert response.status_code in [400, 404]
    
    def test_xss_attempt_in_search(self):
        """Test search endpoint sanitizes XSS attempts"""
        xss_attempt = "<script>alert('xss')</script>"
        response = client.get(f"/api/tools?search={xss_attempt}")
        # Should not return the script tag in any response
        assert "<script>" not in response.text
    
    def test_oversized_request_body(self):
        """Test API rejects oversized request bodies"""
        large_data = {
            "name": "Test Tool",
            "description": "x" * (settings.MAX_CONTENT_LENGTH + 1000)
        }
        response = client.post("/api/tools", json=large_data)
        # Should reject the oversized request
        assert response.status_code in [400, 413, 422]

class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_create_tool_rate_limit(self):
        """Test tool creation endpoint rate limiting"""
        tool_data = {
            "name": "Test Tool",
            "category": "Text Generation"
        }
        
        responses = []
        for _ in range(15):  # Exceed the 10/minute limit
            response = client.post("/api/tools", json=tool_data)
            responses.append(response.status_code)
        
        # Should have some rate limited responses
        assert 429 in responses
    
    def test_content_generation_rate_limit(self):
        """Test content generation endpoint has stricter rate limiting"""
        tool_id = str(uuid.uuid4())
        
        responses = []
        for _ in range(8):  # Exceed the 5/minute limit
            response = client.post(f"/api/tools/{tool_id}/generate-content")
            responses.append(response.status_code)
        
        # Should have some rate limited responses
        assert 429 in responses

if __name__ == "__main__":
    pytest.main([__file__])
