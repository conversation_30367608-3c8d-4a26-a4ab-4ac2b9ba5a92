from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>outer, HTTPException, Request, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import os
import logging
import asyncio
from pathlib import Path
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
import uuid
from datetime import datetime, timedelta
import re
import json
from emergentintegrations.llm.chat import LlmChat, UserMessage

# Import our custom modules
from .config import settings, QUALITY_SCORE_WEIGHTS, DEFAULT_PAGE_SIZE, MAX_PAGE_SIZE
from .security import (
    SecurityMiddleware, InputValidator, sanitize_error_message,
    create_access_token, verify_token, security
)
from .database import db_manager, db_operations

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Load environment
ROOT_DIR = Path(__file__).parent
if (ROOT_DIR / '.env').exists():
    from dotenv import load_dotenv
    load_dotenv(ROOT_DIR / '.env')

# Create the main app
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else settings.ALLOWED_ORIGINS
)

# Add CORS middleware with proper configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.ALLOWED_METHODS,
    allow_headers=settings.ALLOWED_HEADERS,
)

# Add rate limiting
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Create a router with the /api prefix
api_router = APIRouter(prefix=settings.API_V1_STR)

# Supporting Data Models with validation
class PricingTier(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    price: float = Field(..., ge=0)
    currency: str = Field(default="USD", regex=r"^[A-Z]{3}$")
    billing_cycle: str = Field(..., regex=r"^(monthly|yearly|one-time|usage-based)$")
    features: List[str] = Field(default=[], max_items=20)
    is_popular: Optional[bool] = False
    description: Optional[str] = Field(None, max_length=500)

class PricingInfo(BaseModel):
    type: str = Field(..., regex=r"^(free|freemium|paid|open source|subscription)$")
    starting_price: Optional[float] = Field(None, ge=0)
    currency: str = Field(default="USD", regex=r"^[A-Z]{3}$")
    billing_cycle: Optional[str] = Field(None, regex=r"^(monthly|yearly|one-time|usage-based)$")
    tiers: Optional[List[PricingTier]] = Field(default=[], max_items=10)
    free_trial_days: Optional[int] = Field(None, ge=0, le=365)
    has_free_plan: Optional[bool] = None
    description: Optional[str] = Field(None, max_length=500)

class ProsAndCons(BaseModel):
    pros: List[str] = Field(default=[], max_items=20)
    cons: List[str] = Field(default=[], max_items=20)

    @validator('pros', 'cons')
    def validate_items(cls, v):
        return [item.strip() for item in v if item.strip()]

class FAQ(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    question: str = Field(..., min_length=5, max_length=500)
    answer: str = Field(..., min_length=10, max_length=2000)
    category: Optional[str] = Field(None, regex=r"^(general|pricing|features|support|getting-started)$")
    tags: Optional[List[str]] = Field(default=[], max_items=10)
    is_active: Optional[bool] = True
    is_featured: Optional[bool] = False

class SocialLinks(BaseModel):
    twitter: Optional[str] = Field(None, max_length=2048)
    linkedin: Optional[str] = Field(None, max_length=2048)
    github: Optional[str] = Field(None, max_length=2048)
    facebook: Optional[str] = Field(None, max_length=2048)
    youtube: Optional[str] = Field(None, max_length=2048)
    discord: Optional[str] = Field(None, max_length=2048)
    telegram: Optional[str] = Field(None, max_length=2048)
    instagram: Optional[str] = Field(None, max_length=2048)
    tiktok: Optional[str] = Field(None, max_length=2048)
    reddit: Optional[str] = Field(None, max_length=2048)

    @validator('*')
    def validate_urls(cls, v):
        if v and not SecurityMiddleware.validate_url(v):
            raise ValueError('Invalid URL format')
        return v

class Tag(BaseModel):
    type: str = Field(..., regex=r"^(Trending|New|Premium|Featured|Popular)$")
    color: Optional[str] = Field(None, regex=r"^#[0-9A-Fa-f]{6}$")
    description: Optional[str] = Field(None, max_length=200)

class Reviews(BaseModel):
    rating: float = Field(default=0.0, ge=0.0, le=5.0)
    total_reviews: int = Field(default=0, ge=0)

class ClaimInfo(BaseModel):
    is_claimed: bool = False
    claimed_by: Optional[str] = Field(None, max_length=100)
    claimed_at: Optional[datetime] = None
    verification_status: Optional[str] = Field(None, regex=r"^(pending|verified|rejected)$")

# Main AITool Model with comprehensive validation
class AITool(BaseModel):
    # === CORE IDENTIFICATION ===
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., min_length=2, max_length=100)
    slug: str = Field(default="", max_length=100)

    # === VISUAL ASSETS ===
    logo_url: Optional[str] = Field(None, max_length=2048)
    screenshots: Optional[List[str]] = Field(default=[], max_items=10)

    # === DESCRIPTIONS ===
    description: Optional[str] = Field(None, max_length=2000)
    short_description: Optional[str] = Field(None, max_length=500)
    detailed_description: Optional[str] = Field(None, max_length=5000)

    # === NAVIGATION & LINKS ===
    link: Optional[str] = Field(None, max_length=2048)
    website: Optional[str] = Field(None, max_length=2048)

    # === CATEGORIZATION ===
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    company: Optional[str] = Field(None, max_length=100)

    # === STATUS & VERIFICATION ===
    is_verified: Optional[bool] = False
    is_claimed: Optional[bool] = False
    content_status: Optional[str] = Field(default="draft", regex=r"^(published|draft|under_review)$")

    # === RICH CONTENT ===
    features: Optional[List[str]] = Field(default=[], max_items=20)
    pricing: Optional[PricingInfo] = None
    pros_and_cons: Optional[ProsAndCons] = None

    # === FAQ SYSTEM ===
    faqs: Optional[List[FAQ]] = Field(default=[], max_items=20)

    # === SOCIAL & REVIEWS ===
    social_links: Optional[SocialLinks] = None
    reviews: Optional[Reviews] = None

    # === METADATA & TAGS ===
    tags: Optional[List[Tag]] = Field(default=[], max_items=10)
    hashtags: Optional[List[str]] = Field(default=[], max_items=20)
    haiku: Optional[str] = Field(None, max_length=500)

    # === SEO METADATA ===
    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)
    meta_keywords: Optional[str] = Field(None, max_length=500)

    # === AI GENERATION METADATA ===
    ai_generation_status: str = Field(default="pending", regex=r"^(pending|processing|completed|failed)$")
    content_quality_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    last_ai_update: Optional[datetime] = None

    # === VERSIONING & AUDIT ===
    version: int = Field(default=1, ge=1)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    published_at: Optional[datetime] = None

    # === CLAIM INFORMATION ===
    claim_info: Optional[ClaimInfo] = None

    # === LEGACY AI FIELDS (for backward compatibility) ===
    ai_description: Optional[str] = Field(None, max_length=2000)
    ai_features: Optional[List[str]] = Field(default=[], max_items=20)
    ai_pricing: Optional[str] = Field(None, max_length=1000)
    ai_pros: Optional[List[str]] = Field(default=[], max_items=20)
    ai_cons: Optional[List[str]] = Field(default=[], max_items=20)
    ai_use_cases: Optional[List[str]] = Field(default=[], max_items=20)
    ai_comparison: Optional[str] = Field(None, max_length=2000)
    quality_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    generation_status: str = Field(default="pending", regex=r"^(pending|processing|completed|failed)$")

    @validator('name')
    def validate_name(cls, v):
        return InputValidator.validate_tool_name(v)

    @validator('category')
    def validate_category(cls, v):
        if v:
            return InputValidator.validate_category(v)
        return v

    @validator('logo_url', 'website')
    def validate_urls(cls, v, field):
        if v:
            return InputValidator.validate_url_field(v, field.name)
        return v

    @validator('screenshots')
    def validate_screenshots(cls, v):
        return InputValidator.validate_screenshots(v or [])

    @validator('features', 'ai_features', 'ai_pros', 'ai_cons', 'ai_use_cases', 'hashtags')
    def validate_string_lists(cls, v):
        if not v:
            return []
        return [item.strip() for item in v if item and item.strip()]

    def __init__(self, **data):
        super().__init__(**data)
        # Auto-generate slug if not provided
        if not self.slug and self.name:
            self.slug = self.generate_slug(self.name)
        # Auto-generate internal link
        if not self.link and self.slug:
            self.link = f"/tools/{self.slug}"

    @staticmethod
    def generate_slug(name: str) -> str:
        """Generate URL-friendly slug from name"""
        slug = re.sub(r'[^a-zA-Z0-9\s-]', '', name.lower())
        slug = re.sub(r'\s+', '-', slug)
        slug = re.sub(r'-+', '-', slug)
        return slug.strip('-')

class AIToolCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    website: Optional[str] = Field(None, max_length=2048)
    category: Optional[str] = Field(None, max_length=100)
    short_description: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    logo_url: Optional[str] = Field(None, max_length=2048)
    screenshots: Optional[List[str]] = Field(default=[], max_items=10)
    social_links: Optional[SocialLinks] = None
    tags: Optional[List[Tag]] = Field(default=[], max_items=10)

    @validator('name')
    def validate_name(cls, v):
        return InputValidator.validate_tool_name(v)

    @validator('category')
    def validate_category(cls, v):
        if v:
            return InputValidator.validate_category(v)
        return v

    @validator('website', 'logo_url')
    def validate_urls(cls, v, field):
        if v:
            return InputValidator.validate_url_field(v, field.name)
        return v

    @validator('screenshots')
    def validate_screenshots(cls, v):
        return InputValidator.validate_screenshots(v or [])

class AIToolUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    website: Optional[str] = Field(None, max_length=2048)
    category: Optional[str] = Field(None, max_length=100)
    short_description: Optional[str] = Field(None, max_length=500)
    detailed_description: Optional[str] = Field(None, max_length=5000)
    company: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    logo_url: Optional[str] = Field(None, max_length=2048)
    screenshots: Optional[List[str]] = Field(None, max_items=10)
    social_links: Optional[SocialLinks] = None
    tags: Optional[List[Tag]] = Field(None, max_items=10)
    hashtags: Optional[List[str]] = Field(None, max_items=20)
    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)
    meta_keywords: Optional[str] = Field(None, max_length=500)
    content_status: Optional[str] = Field(None, regex=r"^(published|draft|under_review)$")
    is_verified: Optional[bool] = None
    features: Optional[List[str]] = Field(None, max_items=20)
    pricing: Optional[PricingInfo] = None
    pros_and_cons: Optional[ProsAndCons] = None
    faqs: Optional[List[FAQ]] = Field(None, max_items=20)

    @validator('name')
    def validate_name(cls, v):
        if v:
            return InputValidator.validate_tool_name(v)
        return v

    @validator('category')
    def validate_category(cls, v):
        if v:
            return InputValidator.validate_category(v)
        return v

    @validator('website', 'logo_url')
    def validate_urls(cls, v, field):
        if v:
            return InputValidator.validate_url_field(v, field.name)
        return v

    @validator('screenshots')
    def validate_screenshots(cls, v):
        if v is not None:
            return InputValidator.validate_screenshots(v)
        return v

class ContentGenerationRequest(BaseModel):
    tool_id: str

class ContentGenerationResponse(BaseModel):
    success: bool
    message: str
    tool_id: str
    quality_score: Optional[float] = None

# AI Content Generation System with improved error handling
class AIContentGenerator:
    def __init__(self):
        self.openai_api_key = settings.OPENAI_API_KEY
        self.timeout = settings.AI_GENERATION_TIMEOUT
        self.max_tokens = settings.MAX_TOKENS

    async def generate_comprehensive_content(self, tool: AITool) -> Dict[str, Any]:
        """Generate comprehensive AI tool content using OpenAI GPT-4o with timeout"""
        try:
            # Create a new chat instance for this generation
            chat = LlmChat(
                api_key=self.openai_api_key,
                session_id=f"content_gen_{tool.id}_{int(datetime.now().timestamp())}",
                system_message="""You are an expert AI tool analyst and content creator. Your job is to create comprehensive, accurate, and engaging content about AI tools.

You must respond in valid JSON format with the following structure:
{
    "description": "Detailed 2-3 paragraph description of the tool",
    "detailed_description": "Extended 4-5 paragraph detailed description",
    "features": ["Feature 1", "Feature 2", ...],
    "pricing": {
        "type": "freemium|paid|free|subscription",
        "starting_price": 0,
        "currency": "USD",
        "billing_cycle": "monthly|yearly|one-time",
        "has_free_plan": true,
        "description": "Pricing description"
    },
    "pros": ["Pro 1", "Pro 2", ...],
    "cons": ["Con 1", "Con 2", ...],
    "use_cases": ["Use case 1", "Use case 2", ...],
    "comparison": "How this tool compares to similar tools in the market",
    "faqs": [
        {
            "question": "FAQ question",
            "answer": "FAQ answer",
            "category": "general|pricing|features|support|getting-started"
        }
    ],
    "meta_title": "SEO optimized title (60 chars max)",
    "meta_description": "SEO optimized description (160 chars max)",
    "meta_keywords": "keyword1, keyword2, keyword3",
    "hashtags": ["#hashtag1", "#hashtag2", ...],
    "haiku": "Creative haiku about the tool (3 lines, 5-7-5 syllables)"
}

Make the content professional, informative, and balanced. Focus on practical value for users."""
            ).with_model("openai", "gpt-4o").with_max_tokens(self.max_tokens)
            
            # Create comprehensive prompt with sanitized input
            sanitized_name = SecurityMiddleware.sanitize_string(tool.name or "", 100)
            sanitized_website = SecurityMiddleware.sanitize_string(tool.website or "", 200)
            sanitized_category = SecurityMiddleware.sanitize_string(tool.category or "", 100)
            sanitized_company = SecurityMiddleware.sanitize_string(tool.company or "", 100)
            sanitized_description = SecurityMiddleware.sanitize_string(tool.short_description or "", 500)

            user_message = UserMessage(
                text=f"""Please analyze and generate comprehensive content for this AI tool:

Tool Name: {sanitized_name}
Website: {sanitized_website}
Category: {sanitized_category}
Company: {sanitized_company}
Basic Description: {sanitized_description}

Generate detailed content including:
1. A comprehensive description (2-3 paragraphs)
2. Extended detailed description (4-5 paragraphs)
3. Key features (8-12 features)
4. Pricing information with structure
5. Pros and cons (4-6 each)
6. Use cases (6-8 use cases)
7. Market comparison
8. FAQs (5-8 questions)
9. SEO metadata
10. Social hashtags
11. Creative haiku

Respond in valid JSON format only."""
            )

            # Generate content with timeout
            try:
                response = await asyncio.wait_for(
                    chat.send_message(user_message),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail="AI content generation timed out"
                )

            # Parse JSON response with improved error handling
            try:
                content_data = json.loads(response)
            except json.JSONDecodeError as e:
                logger.warning(f"Initial JSON parsing failed: {str(e)}")
                # Try to extract JSON from the response
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    try:
                        content_data = json.loads(json_match.group())
                    except json.JSONDecodeError:
                        raise HTTPException(
                            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                            detail="AI returned malformed JSON response"
                        )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail="AI response does not contain valid JSON"
                    )

            # Validate and sanitize the generated content
            content_data = self._sanitize_generated_content(content_data)

            return content_data

        except HTTPException:
            raise
        except Exception as e:
            error_msg = sanitize_error_message(str(e))
            logger.error(f"Error generating content for tool {tool.id}: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate AI content"
            )
    
    def _sanitize_generated_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize AI-generated content"""
        sanitized = {}

        # Sanitize text fields
        text_fields = ['description', 'detailed_description', 'comparison', 'meta_title', 'meta_description', 'meta_keywords', 'haiku']
        for field in text_fields:
            if field in content:
                max_length = 2000 if field in ['description', 'detailed_description', 'comparison'] else 500
                sanitized[field] = SecurityMiddleware.sanitize_string(content[field], max_length)

        # Sanitize list fields
        list_fields = ['features', 'pros', 'cons', 'use_cases', 'hashtags']
        for field in list_fields:
            if field in content and isinstance(content[field], list):
                sanitized[field] = [
                    SecurityMiddleware.sanitize_string(item, 200)
                    for item in content[field][:20]  # Limit to 20 items
                    if item and isinstance(item, str)
                ]

        # Sanitize FAQs
        if 'faqs' in content and isinstance(content['faqs'], list):
            sanitized['faqs'] = []
            for faq in content['faqs'][:20]:  # Limit to 20 FAQs
                if isinstance(faq, dict) and 'question' in faq and 'answer' in faq:
                    sanitized_faq = {
                        'question': SecurityMiddleware.sanitize_string(faq['question'], 500),
                        'answer': SecurityMiddleware.sanitize_string(faq['answer'], 2000),
                        'category': faq.get('category', 'general')
                    }
                    sanitized['faqs'].append(sanitized_faq)

        # Sanitize pricing
        if 'pricing' in content and isinstance(content['pricing'], dict):
            pricing = content['pricing']
            sanitized['pricing'] = {
                'type': pricing.get('type', 'unknown'),
                'starting_price': pricing.get('starting_price', 0) if isinstance(pricing.get('starting_price'), (int, float)) else 0,
                'currency': pricing.get('currency', 'USD'),
                'billing_cycle': pricing.get('billing_cycle', 'monthly'),
                'has_free_plan': bool(pricing.get('has_free_plan', False)),
                'description': SecurityMiddleware.sanitize_string(pricing.get('description', ''), 500)
            }

        return sanitized

    async def calculate_quality_score(self, content: Dict[str, Any]) -> float:
        """Calculate quality score based on content completeness and depth using constants"""
        try:
            score = 0.0
            weights = QUALITY_SCORE_WEIGHTS

            # Description completeness
            if content.get('description'):
                desc_words = len(content['description'].split())
                if desc_words >= weights['GOOD_DESCRIPTION_WORDS']:
                    score += weights['DESCRIPTION_WEIGHT']
                elif desc_words >= weights['MIN_DESCRIPTION_WORDS']:
                    score += weights['DESCRIPTION_WEIGHT'] * 0.8
                else:
                    score += weights['DESCRIPTION_WEIGHT'] * 0.4

            # Detailed description
            if content.get('detailed_description'):
                detailed_words = len(content['detailed_description'].split())
                if detailed_words >= weights['GOOD_DETAILED_WORDS']:
                    score += weights['DETAILED_DESCRIPTION_WEIGHT']
                elif detailed_words >= weights['MIN_DETAILED_WORDS']:
                    score += weights['DETAILED_DESCRIPTION_WEIGHT'] * 0.67
                else:
                    score += weights['DETAILED_DESCRIPTION_WEIGHT'] * 0.33

            # Features completeness
            features = content.get('features', [])
            if len(features) >= weights['GOOD_FEATURES']:
                score += weights['FEATURES_WEIGHT']
            elif len(features) >= weights['MIN_FEATURES']:
                score += weights['FEATURES_WEIGHT'] * 0.8
            else:
                score += weights['FEATURES_WEIGHT'] * 0.27

            # Pricing info
            pricing = content.get('pricing', {})
            if pricing and isinstance(pricing, dict):
                if pricing.get('type') and pricing.get('description'):
                    score += weights['PRICING_WEIGHT']
                elif pricing.get('type'):
                    score += weights['PRICING_WEIGHT'] * 0.7
                else:
                    score += weights['PRICING_WEIGHT'] * 0.3

            # Pros/Cons balance
            pros = content.get('pros', [])
            cons = content.get('cons', [])
            if len(pros) >= weights['GOOD_PROS_CONS'] and len(cons) >= weights['GOOD_PROS_CONS']:
                score += weights['PROS_CONS_WEIGHT']
            elif len(pros) >= weights['MIN_PROS_CONS'] and len(cons) >= weights['MIN_PROS_CONS']:
                score += weights['PROS_CONS_WEIGHT'] * 0.8
            else:
                score += weights['PROS_CONS_WEIGHT'] * 0.27

            # Use cases
            use_cases = content.get('use_cases', [])
            if len(use_cases) >= weights['GOOD_USE_CASES']:
                score += weights['USE_CASES_WEIGHT']
            elif len(use_cases) >= weights['MIN_USE_CASES']:
                score += weights['USE_CASES_WEIGHT'] * 0.8
            else:
                score += weights['USE_CASES_WEIGHT'] * 0.2

            # FAQs
            faqs = content.get('faqs', [])
            if len(faqs) >= weights['GOOD_FAQS']:
                score += weights['FAQS_WEIGHT']
            elif len(faqs) >= weights['MIN_FAQS']:
                score += weights['FAQS_WEIGHT'] * 0.6
            else:
                score += weights['FAQS_WEIGHT'] * 0.2

            # SEO metadata
            if content.get('meta_title') and content.get('meta_description'):
                score += weights['SEO_WEIGHT']
            elif content.get('meta_title') or content.get('meta_description'):
                score += weights['SEO_WEIGHT'] * 0.67
            else:
                score += weights['SEO_WEIGHT'] * 0.33

            # Creative content
            if content.get('haiku') and content.get('hashtags'):
                score += weights['CREATIVE_WEIGHT']
            elif content.get('haiku') or content.get('hashtags'):
                score += weights['CREATIVE_WEIGHT'] * 0.5

            return min(score, 100.0)  # Cap at 100

        except Exception as e:
            error_msg = sanitize_error_message(str(e))
            logger.error(f"Error calculating quality score: {error_msg}")
            return 70.0  # Default score if calculation fails

# Initialize AI Content Generator
ai_generator = AIContentGenerator()

# Utility functions
def create_slug(name: str) -> str:
    """Create URL-friendly slug"""
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', name.lower())
    slug = re.sub(r'\s+', '-', slug)
    slug = re.sub(r'-+', '-', slug)
    return slug.strip('-')

# Response models
class PaginatedResponse(BaseModel):
    items: List[AITool]
    pagination: Dict[str, Any]

class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    database: Dict[str, Any]
    version: str

# Middleware to add security headers
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    return SecurityMiddleware.add_security_headers(response)

# API Routes
@api_router.get("/", tags=["System"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def root(request: Request):
    """API root endpoint"""
    return {
        "message": f"{settings.PROJECT_NAME} - Content Generation Engine",
        "version": settings.VERSION,
        "docs": "/docs" if settings.DEBUG else None
    }

@api_router.get("/health", response_model=HealthResponse, tags=["System"])
@limiter.limit("10/minute")
async def health_check(request: Request):
    """Health check endpoint"""
    db_health = await db_manager.health_check()
    return HealthResponse(
        status="healthy" if db_health["status"] == "healthy" else "degraded",
        timestamp=datetime.utcnow(),
        database=db_health,
        version=settings.VERSION
    )

@api_router.post("/tools", response_model=AITool, tags=["Tools"])
@limiter.limit("10/minute")
async def create_ai_tool(request: Request, tool_data: AIToolCreate):
    """Create a new AI tool entry"""
    try:
        # Convert to dict and create AITool
        tool_dict = tool_data.dict()
        tool = AITool(**tool_dict)

        # Ensure slug is unique
        collection = db_manager.get_collection("ai_tools")
        existing_tool = await collection.find_one({"slug": tool.slug})
        if existing_tool:
            # Add timestamp to make it unique
            tool.slug = f"{tool.slug}-{int(datetime.now().timestamp())}"
            tool.link = f"/tools/{tool.slug}"

        # Insert with error handling
        await collection.insert_one(tool.dict())
        logger.info(f"Created new AI tool: {tool.name} (ID: {tool.id})")
        return tool

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error creating AI tool: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create AI tool"
        )

@api_router.get("/tools", response_model=PaginatedResponse, tags=["Tools"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def get_ai_tools(
    request: Request,
    page: int = Field(default=1, ge=1),
    page_size: int = Field(default=DEFAULT_PAGE_SIZE, ge=1, le=MAX_PAGE_SIZE),
    category: Optional[str] = Field(None, max_length=100),
    status: Optional[str] = Field(None, regex=r"^(published|draft|under_review)$"),
    verified: Optional[bool] = None,
    search: Optional[str] = Field(None, max_length=200)
):
    """Get AI tools with pagination and filtering"""
    try:
        # Build query with input validation
        query = {}

        if category:
            # Validate category
            try:
                InputValidator.validate_category(category)
                query["category"] = category
            except HTTPException:
                # Invalid category, ignore filter
                pass

        if status:
            query["content_status"] = status

        if verified is not None:
            query["is_verified"] = verified

        if search:
            # Sanitize search term
            sanitized_search = SecurityMiddleware.sanitize_string(search, 200)
            if sanitized_search:
                query["$or"] = [
                    {"name": {"$regex": sanitized_search, "$options": "i"}},
                    {"short_description": {"$regex": sanitized_search, "$options": "i"}},
                    {"description": {"$regex": sanitized_search, "$options": "i"}},
                    {"company": {"$regex": sanitized_search, "$options": "i"}}
                ]

        # Get paginated results
        result = await db_operations.get_tools_with_pagination(
            page=page,
            page_size=page_size,
            filters=query,
            sort_by="created_at",
            sort_order=-1
        )

        # Convert to AITool objects
        tools = [AITool(**tool) for tool in result["tools"]]

        return PaginatedResponse(
            items=tools,
            pagination=result["pagination"]
        )

    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error fetching AI tools: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch AI tools"
        )

@api_router.get("/tools/slug/{slug}", response_model=AITool, tags=["Tools"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def get_ai_tool_by_slug(request: Request, slug: str = Field(..., max_length=100)):
    """Get a specific AI tool by slug"""
    try:
        # Sanitize slug
        sanitized_slug = SecurityMiddleware.sanitize_string(slug, 100)
        if not sanitized_slug:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid slug"
            )

        collection = db_manager.get_collection("ai_tools")
        tool = await collection.find_one({"slug": sanitized_slug})
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )
        return AITool(**tool)
    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error fetching AI tool by slug {slug}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch AI tool"
        )

@api_router.get("/tools/{tool_id}", response_model=AITool, tags=["Tools"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def get_ai_tool(request: Request, tool_id: str = Field(..., max_length=36)):
    """Get a specific AI tool by ID"""
    try:
        # Validate UUID format
        if not SecurityMiddleware.validate_uuid(tool_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tool ID format"
            )

        collection = db_manager.get_collection("ai_tools")
        tool = await collection.find_one({"id": tool_id})
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )
        return AITool(**tool)
    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error fetching AI tool {tool_id}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch AI tool"
        )

@api_router.put("/tools/{tool_id}", response_model=AITool, tags=["Tools"])
@limiter.limit("20/minute")
async def update_ai_tool(request: Request, tool_id: str, tool_data: AIToolUpdate):
    """Update an AI tool"""
    try:
        # Validate UUID format
        if not SecurityMiddleware.validate_uuid(tool_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tool ID format"
            )

        collection = db_manager.get_collection("ai_tools")

        # Get existing tool
        existing_tool = await collection.find_one({"id": tool_id})
        if not existing_tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )

        # Update fields
        update_data = {k: v for k, v in tool_data.model_dump(exclude_unset=True).items() if v is not None}
        update_data["updated_at"] = datetime.now(datetime.timezone.utc)
        update_data["version"] = existing_tool.get("version", 1) + 1

        # Update slug if name changed
        if "name" in update_data:
            new_slug = create_slug(update_data["name"])
            # Check if slug is unique
            slug_exists = await collection.find_one({"slug": new_slug, "id": {"$ne": tool_id}})
            if not slug_exists:
                update_data["slug"] = new_slug
                update_data["link"] = f"/tools/{new_slug}"

        await collection.update_one(
            {"id": tool_id},
            {"$set": update_data}
        )

        # Return updated tool
        updated_tool = await collection.find_one({"id": tool_id})
        logger.info(f"Updated AI tool: {tool_id}")
        return AITool(**updated_tool)
    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error updating AI tool {tool_id}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update AI tool"
        )

@api_router.delete("/tools/{tool_id}", tags=["Tools"])
@limiter.limit("10/minute")
async def delete_ai_tool(request: Request, tool_id: str):
    """Delete an AI tool"""
    try:
        # Validate UUID format
        if not SecurityMiddleware.validate_uuid(tool_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tool ID format"
            )

        collection = db_manager.get_collection("ai_tools")
        result = await collection.delete_one({"id": tool_id})
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )
        logger.info(f"Deleted AI tool: {tool_id}")
        return {"message": "AI tool deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error deleting AI tool {tool_id}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete AI tool"
        )

@api_router.post("/tools/{tool_id}/generate-content", response_model=ContentGenerationResponse, tags=["AI Content"])
@limiter.limit("5/minute")
async def generate_tool_content(request: Request, tool_id: str):
    """Generate AI content for a specific tool"""
    try:
        # Validate UUID format
        if not SecurityMiddleware.validate_uuid(tool_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tool ID format"
            )

        collection = db_manager.get_collection("ai_tools")

        # Get the tool
        tool_data = await collection.find_one({"id": tool_id})
        if not tool_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )

        tool = AITool(**tool_data)

        # Update status to processing
        await collection.update_one(
            {"id": tool_id},
            {"$set": {
                "ai_generation_status": "processing",
                "generation_status": "processing",
                "updated_at": datetime.now(datetime.timezone.utc)
            }}
        )

        try:
            # Generate content with timeout
            content = await ai_generator.generate_comprehensive_content(tool)

            # Calculate quality score
            quality_score = await ai_generator.calculate_quality_score(content)

            # Prepare update data
            update_data = {
                "ai_generation_status": "completed",
                "generation_status": "completed",
                "content_quality_score": quality_score,
                "quality_score": quality_score,
                "last_ai_update": datetime.now(datetime.timezone.utc),
                "updated_at": datetime.now(datetime.timezone.utc)
            }
            
            # Update with AI-generated content
            if content.get("description"):
                update_data["description"] = content["description"]
                update_data["ai_description"] = content["description"]
            
            if content.get("detailed_description"):
                update_data["detailed_description"] = content["detailed_description"]
            
            if content.get("features"):
                update_data["features"] = content["features"]
                update_data["ai_features"] = content["features"]
            
            if content.get("pricing"):
                update_data["pricing"] = content["pricing"]
                update_data["ai_pricing"] = str(content["pricing"])
            
            if content.get("pros") or content.get("cons"):
                update_data["pros_and_cons"] = ProsAndCons(
                    pros=content.get("pros", []),
                    cons=content.get("cons", [])
                ).model_dump()
                update_data["ai_pros"] = content.get("pros", [])
                update_data["ai_cons"] = content.get("cons", [])

            if content.get("use_cases"):
                update_data["ai_use_cases"] = content["use_cases"]

            if content.get("comparison"):
                update_data["ai_comparison"] = content["comparison"]

            if content.get("faqs"):
                faqs = []
                for faq_data in content["faqs"]:
                    faq = FAQ(**faq_data)
                    faqs.append(faq.model_dump())
                update_data["faqs"] = faqs

            if content.get("meta_title"):
                update_data["meta_title"] = content["meta_title"]

            if content.get("meta_description"):
                update_data["meta_description"] = content["meta_description"]

            if content.get("meta_keywords"):
                update_data["meta_keywords"] = content["meta_keywords"]

            if content.get("hashtags"):
                update_data["hashtags"] = content["hashtags"]

            if content.get("haiku"):
                update_data["haiku"] = content["haiku"]

            await collection.update_one({"id": tool_id}, {"$set": update_data})
            
            return ContentGenerationResponse(
                success=True,
                message="Content generated successfully",
                tool_id=tool_id,
                quality_score=quality_score
            )
            
        except Exception as e:
            # Update status to failed
            await collection.update_one(
                {"id": tool_id},
                {"$set": {
                    "ai_generation_status": "failed",
                    "generation_status": "failed",
                    "updated_at": datetime.now(datetime.timezone.utc)
                }}
            )
            raise e

    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error generating content for tool {tool_id}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate content"
        )

@api_router.post("/tools/{tool_id}/publish", tags=["Tools"])
@limiter.limit("10/minute")
async def publish_tool(request: Request, tool_id: str):
    """Publish an AI tool"""
    try:
        # Validate UUID format
        if not SecurityMiddleware.validate_uuid(tool_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tool ID format"
            )

        collection = db_manager.get_collection("ai_tools")
        tool = await collection.find_one({"id": tool_id})
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI tool not found"
            )

        await collection.update_one(
            {"id": tool_id},
            {"$set": {
                "content_status": "published",
                "published_at": datetime.now(datetime.timezone.utc),
                "updated_at": datetime.now(datetime.timezone.utc)
            }}
        )

        logger.info(f"Published AI tool: {tool_id}")
        return {"message": "AI tool published successfully"}
    except HTTPException:
        raise
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error publishing AI tool {tool_id}: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish AI tool"
        )

@api_router.get("/categories", tags=["System"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def get_categories(request: Request):
    """Get all unique categories"""
    try:
        collection = db_manager.get_collection("ai_tools")
        categories = await collection.distinct("category")
        return {"categories": [cat for cat in categories if cat]}
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error fetching categories: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch categories"
        )

@api_router.get("/stats", tags=["System"])
@limiter.limit(f"{settings.RATE_LIMIT_REQUESTS}/minute")
async def get_stats(request: Request):
    """Get system statistics using optimized aggregation"""
    try:
        stats = await db_operations.get_statistics_optimized()
        return stats
    except Exception as e:
        error_msg = sanitize_error_message(str(e))
        logger.error(f"Error fetching stats: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch statistics"
        )

# Include the router in the main app
app.include_router(api_router)

# Application startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize database connection and create indexes on startup"""
    try:
        logger.info("Starting DudeAI AI Agent System...")

        # Connect to database
        connected = await db_manager.connect()
        if not connected:
            logger.error("Failed to connect to database")
            raise RuntimeError("Database connection failed")

        logger.info("Application startup completed successfully")
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    try:
        logger.info("Shutting down DudeAI AI Agent System...")
        await db_manager.disconnect()
        logger.info("Application shutdown completed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level=settings.LOG_LEVEL.lower()
    )