from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
import uuid
from datetime import datetime
import asyncio
import re
from emergentintegrations.llm.chat import LlmChat, UserMessage

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Create the main app without a prefix
app = FastAPI(title="DudeAI AI Agent System", version="1.0.0")

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Supporting Data Models
class PricingTier(BaseModel):
    name: str
    price: float
    currency: str = "USD"
    billing_cycle: str  # 'monthly', 'yearly', 'one-time', 'usage-based'
    features: List[str] = []
    is_popular: Optional[bool] = False
    description: Optional[str] = None

class PricingInfo(BaseModel):
    type: str  # 'free', 'freemium', 'paid', 'open source', 'subscription'
    starting_price: Optional[float] = None
    currency: str = "USD"
    billing_cycle: Optional[str] = None
    tiers: Optional[List[PricingTier]] = []
    free_trial_days: Optional[int] = None
    has_free_plan: Optional[bool] = None
    description: Optional[str] = None

class ProsAndCons(BaseModel):
    pros: List[str] = []
    cons: List[str] = []

class FAQ(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    question: str
    answer: str
    category: Optional[str] = None  # 'general', 'pricing', 'features', 'support', 'getting-started'
    tags: Optional[List[str]] = []
    is_active: Optional[bool] = True
    is_featured: Optional[bool] = False

class SocialLinks(BaseModel):
    twitter: Optional[str] = None
    linkedin: Optional[str] = None
    github: Optional[str] = None
    facebook: Optional[str] = None
    youtube: Optional[str] = None
    discord: Optional[str] = None
    telegram: Optional[str] = None
    instagram: Optional[str] = None
    tiktok: Optional[str] = None
    reddit: Optional[str] = None

class Tag(BaseModel):
    type: str  # 'Trending', 'New', 'Premium', 'Featured', 'Popular'
    color: Optional[str] = None
    description: Optional[str] = None

class Reviews(BaseModel):
    rating: float = 0.0
    total_reviews: int = 0

class ClaimInfo(BaseModel):
    is_claimed: bool = False
    claimed_by: Optional[str] = None
    claimed_at: Optional[datetime] = None
    verification_status: Optional[str] = None

# Main AITool Model
class AITool(BaseModel):
    # === CORE IDENTIFICATION ===
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    slug: str = Field(default="")
    
    # === VISUAL ASSETS ===
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = []
    
    # === DESCRIPTIONS ===
    description: Optional[str] = None
    short_description: Optional[str] = None
    detailed_description: Optional[str] = None
    
    # === NAVIGATION & LINKS ===
    link: Optional[str] = None  # Internal tool page URL
    website: Optional[str] = None  # External website URL
    
    # === CATEGORIZATION ===
    category: Optional[str] = None
    subcategory: Optional[str] = None
    company: Optional[str] = None
    
    # === STATUS & VERIFICATION ===
    is_verified: Optional[bool] = False
    is_claimed: Optional[bool] = False
    content_status: Optional[str] = "draft"  # 'published', 'draft', 'under_review'
    
    # === RICH CONTENT ===
    features: Optional[List[str]] = []
    pricing: Optional[PricingInfo] = None
    pros_and_cons: Optional[ProsAndCons] = None
    
    # === FAQ SYSTEM ===
    faqs: Optional[List[FAQ]] = []
    
    # === SOCIAL & REVIEWS ===
    social_links: Optional[SocialLinks] = None
    reviews: Optional[Reviews] = None
    
    # === METADATA & TAGS ===
    tags: Optional[List[Tag]] = []
    hashtags: Optional[List[str]] = []
    haiku: Optional[str] = None
    
    # === SEO METADATA ===
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    
    # === AI GENERATION METADATA ===
    ai_generation_status: str = "pending"  # 'pending', 'processing', 'completed', 'failed'
    content_quality_score: Optional[float] = None
    last_ai_update: Optional[datetime] = None
    
    # === VERSIONING & AUDIT ===
    version: int = 1
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    published_at: Optional[datetime] = None
    
    # === CLAIM INFORMATION ===
    claim_info: Optional[ClaimInfo] = None
    
    # === LEGACY AI FIELDS (for backward compatibility) ===
    ai_description: Optional[str] = None
    ai_features: Optional[List[str]] = []
    ai_pricing: Optional[str] = None
    ai_pros: Optional[List[str]] = []
    ai_cons: Optional[List[str]] = []
    ai_use_cases: Optional[List[str]] = []
    ai_comparison: Optional[str] = None
    quality_score: Optional[float] = None
    generation_status: str = "pending"

    def __init__(self, **data):
        super().__init__(**data)
        # Auto-generate slug if not provided
        if not self.slug and self.name:
            self.slug = self.generate_slug(self.name)
        # Auto-generate internal link
        if not self.link and self.slug:
            self.link = f"/tools/{self.slug}"

    @staticmethod
    def generate_slug(name: str) -> str:
        """Generate URL-friendly slug from name"""
        slug = re.sub(r'[^a-zA-Z0-9\s-]', '', name.lower())
        slug = re.sub(r'\s+', '-', slug)
        slug = re.sub(r'-+', '-', slug)
        return slug.strip('-')

class AIToolCreate(BaseModel):
    name: str
    website: Optional[str] = None
    category: Optional[str] = None
    short_description: Optional[str] = None
    company: Optional[str] = None
    subcategory: Optional[str] = None
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = []
    social_links: Optional[SocialLinks] = None
    tags: Optional[List[Tag]] = []

class AIToolUpdate(BaseModel):
    name: Optional[str] = None
    website: Optional[str] = None
    category: Optional[str] = None
    short_description: Optional[str] = None
    detailed_description: Optional[str] = None
    company: Optional[str] = None
    subcategory: Optional[str] = None
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = None
    social_links: Optional[SocialLinks] = None
    tags: Optional[List[Tag]] = None
    hashtags: Optional[List[str]] = None
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    content_status: Optional[str] = None
    is_verified: Optional[bool] = None
    features: Optional[List[str]] = None
    pricing: Optional[PricingInfo] = None
    pros_and_cons: Optional[ProsAndCons] = None
    faqs: Optional[List[FAQ]] = None

class ContentGenerationRequest(BaseModel):
    tool_id: str

class ContentGenerationResponse(BaseModel):
    success: bool
    message: str
    tool_id: str
    quality_score: Optional[float] = None

# AI Content Generation System
class AIContentGenerator:
    def __init__(self):
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
    
    async def generate_comprehensive_content(self, tool: AITool) -> Dict[str, Any]:
        """Generate comprehensive AI tool content using OpenAI GPT-4o"""
        try:
            # Create a new chat instance for this generation
            chat = LlmChat(
                api_key=self.openai_api_key,
                session_id=f"content_gen_{tool.id}_{int(datetime.now().timestamp())}",
                system_message="""You are an expert AI tool analyst and content creator. Your job is to create comprehensive, accurate, and engaging content about AI tools.

You must respond in valid JSON format with the following structure:
{
    "description": "Detailed 2-3 paragraph description of the tool",
    "detailed_description": "Extended 4-5 paragraph detailed description",
    "features": ["Feature 1", "Feature 2", ...],
    "pricing": {
        "type": "freemium|paid|free|subscription",
        "starting_price": 0,
        "currency": "USD",
        "billing_cycle": "monthly|yearly|one-time",
        "has_free_plan": true,
        "description": "Pricing description"
    },
    "pros": ["Pro 1", "Pro 2", ...],
    "cons": ["Con 1", "Con 2", ...],
    "use_cases": ["Use case 1", "Use case 2", ...],
    "comparison": "How this tool compares to similar tools in the market",
    "faqs": [
        {
            "question": "FAQ question",
            "answer": "FAQ answer",
            "category": "general|pricing|features|support|getting-started"
        }
    ],
    "meta_title": "SEO optimized title (60 chars max)",
    "meta_description": "SEO optimized description (160 chars max)",
    "meta_keywords": "keyword1, keyword2, keyword3",
    "hashtags": ["#hashtag1", "#hashtag2", ...],
    "haiku": "Creative haiku about the tool (3 lines, 5-7-5 syllables)"
}

Make the content professional, informative, and balanced. Focus on practical value for users."""
            ).with_model("openai", "gpt-4o").with_max_tokens(3000)
            
            # Create comprehensive prompt
            user_message = UserMessage(
                text=f"""Please analyze and generate comprehensive content for this AI tool:

Tool Name: {tool.name}
Website: {tool.website}
Category: {tool.category}
Company: {tool.company}
Basic Description: {tool.short_description}

Generate detailed content including:
1. A comprehensive description (2-3 paragraphs)
2. Extended detailed description (4-5 paragraphs)
3. Key features (8-12 features)
4. Pricing information with structure
5. Pros and cons (4-6 each)
6. Use cases (6-8 use cases)
7. Market comparison
8. FAQs (5-8 questions)
9. SEO metadata
10. Social hashtags
11. Creative haiku

Respond in valid JSON format only."""
            )
            
            # Generate content
            response = await chat.send_message(user_message)
            
            # Parse JSON response
            import json
            try:
                content_data = json.loads(response)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from the response
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    content_data = json.loads(json_match.group())
                else:
                    raise ValueError("Invalid JSON response from AI")
            
            return content_data
            
        except Exception as e:
            logging.error(f"Error generating content for tool {tool.id}: {str(e)}")
            raise
    
    async def calculate_quality_score(self, content: Dict[str, Any]) -> float:
        """Calculate quality score based on content completeness and depth"""
        try:
            score = 0.0
            
            # Description completeness (0-25 points)
            if content.get('description'):
                desc_words = len(content['description'].split())
                if desc_words >= 100:
                    score += 25
                elif desc_words >= 50:
                    score += 20
                else:
                    score += 10
            
            # Detailed description (0-15 points)
            if content.get('detailed_description'):
                detailed_words = len(content['detailed_description'].split())
                if detailed_words >= 200:
                    score += 15
                elif detailed_words >= 100:
                    score += 10
                else:
                    score += 5
            
            # Features completeness (0-15 points)
            features = content.get('features', [])
            if len(features) >= 8:
                score += 15
            elif len(features) >= 5:
                score += 12
            elif len(features) >= 3:
                score += 8
            else:
                score += 4
            
            # Pricing info (0-10 points)
            pricing = content.get('pricing', {})
            if pricing and isinstance(pricing, dict):
                if pricing.get('type') and pricing.get('description'):
                    score += 10
                elif pricing.get('type'):
                    score += 7
                else:
                    score += 3
            
            # Pros/Cons balance (0-15 points)
            pros = content.get('pros', [])
            cons = content.get('cons', [])
            if len(pros) >= 4 and len(cons) >= 4:
                score += 15
            elif len(pros) >= 3 and len(cons) >= 3:
                score += 12
            elif len(pros) >= 2 and len(cons) >= 2:
                score += 8
            else:
                score += 4
            
            # Use cases (0-10 points)
            use_cases = content.get('use_cases', [])
            if len(use_cases) >= 6:
                score += 10
            elif len(use_cases) >= 4:
                score += 8
            elif len(use_cases) >= 2:
                score += 5
            else:
                score += 2
            
            # FAQs (0-5 points)
            faqs = content.get('faqs', [])
            if len(faqs) >= 5:
                score += 5
            elif len(faqs) >= 3:
                score += 3
            else:
                score += 1
            
            # SEO metadata (0-3 points)
            if content.get('meta_title') and content.get('meta_description'):
                score += 3
            elif content.get('meta_title') or content.get('meta_description'):
                score += 2
            else:
                score += 1
            
            # Creative content (0-2 points)
            if content.get('haiku') and content.get('hashtags'):
                score += 2
            elif content.get('haiku') or content.get('hashtags'):
                score += 1
            
            return min(score, 100.0)  # Cap at 100
            
        except Exception as e:
            logging.error(f"Error calculating quality score: {str(e)}")
            return 70.0  # Default score if calculation fails

# Initialize AI Content Generator
ai_generator = AIContentGenerator()

# Utility functions
def create_slug(name: str) -> str:
    """Create URL-friendly slug"""
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', name.lower())
    slug = re.sub(r'\s+', '-', slug)
    slug = re.sub(r'-+', '-', slug)
    return slug.strip('-')

# API Routes
@api_router.get("/")
async def root():
    return {"message": "DudeAI AI Agent System - Content Generation Engine"}

@api_router.post("/tools", response_model=AITool)
async def create_ai_tool(tool_data: AIToolCreate):
    """Create a new AI tool entry"""
    try:
        # Convert to dict and create AITool
        tool_dict = tool_data.dict()
        tool = AITool(**tool_dict)
        
        # Ensure slug is unique
        existing_tool = await db.ai_tools.find_one({"slug": tool.slug})
        if existing_tool:
            # Add timestamp to make it unique
            tool.slug = f"{tool.slug}-{int(datetime.now().timestamp())}"
            tool.link = f"/tools/{tool.slug}"
        
        await db.ai_tools.insert_one(tool.dict())
        return tool
    except Exception as e:
        logging.error(f"Error creating AI tool: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create AI tool")

@api_router.get("/tools", response_model=List[AITool])
async def get_ai_tools(
    limit: int = 50, 
    category: Optional[str] = None,
    status: Optional[str] = None,
    verified: Optional[bool] = None,
    search: Optional[str] = None
):
    """Get all AI tools with optional filters"""
    try:
        query = {}
        if category:
            query["category"] = category
        if status:
            query["content_status"] = status
        if verified is not None:
            query["is_verified"] = verified
        if search:
            query["$or"] = [
                {"name": {"$regex": search, "$options": "i"}},
                {"short_description": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}}
            ]
        
        tools = await db.ai_tools.find(query).sort("created_at", -1).limit(limit).to_list(limit)
        return [AITool(**tool) for tool in tools]
    except Exception as e:
        logging.error(f"Error fetching AI tools: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch AI tools")

@api_router.get("/tools/slug/{slug}", response_model=AITool)
async def get_ai_tool_by_slug(slug: str):
    """Get a specific AI tool by slug"""
    try:
        tool = await db.ai_tools.find_one({"slug": slug})
        if not tool:
            raise HTTPException(status_code=404, detail="AI tool not found")
        return AITool(**tool)
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error fetching AI tool by slug {slug}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch AI tool")

@api_router.get("/tools/{tool_id}", response_model=AITool)
async def get_ai_tool(tool_id: str):
    """Get a specific AI tool by ID"""
    try:
        tool = await db.ai_tools.find_one({"id": tool_id})
        if not tool:
            raise HTTPException(status_code=404, detail="AI tool not found")
        return AITool(**tool)
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error fetching AI tool {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch AI tool")

@api_router.put("/tools/{tool_id}", response_model=AITool)
async def update_ai_tool(tool_id: str, tool_data: AIToolUpdate):
    """Update an AI tool"""
    try:
        # Get existing tool
        existing_tool = await db.ai_tools.find_one({"id": tool_id})
        if not existing_tool:
            raise HTTPException(status_code=404, detail="AI tool not found")
        
        # Update fields
        update_data = {k: v for k, v in tool_data.dict().items() if v is not None}
        update_data["updated_at"] = datetime.utcnow()
        update_data["version"] = existing_tool.get("version", 1) + 1
        
        # Update slug if name changed
        if "name" in update_data:
            new_slug = create_slug(update_data["name"])
            # Check if slug is unique
            slug_exists = await db.ai_tools.find_one({"slug": new_slug, "id": {"$ne": tool_id}})
            if not slug_exists:
                update_data["slug"] = new_slug
                update_data["link"] = f"/tools/{new_slug}"
        
        await db.ai_tools.update_one(
            {"id": tool_id},
            {"$set": update_data}
        )
        
        # Return updated tool
        updated_tool = await db.ai_tools.find_one({"id": tool_id})
        return AITool(**updated_tool)
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error updating AI tool {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update AI tool")

@api_router.delete("/tools/{tool_id}")
async def delete_ai_tool(tool_id: str):
    """Delete an AI tool"""
    try:
        result = await db.ai_tools.delete_one({"id": tool_id})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI tool not found")
        return {"message": "AI tool deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error deleting AI tool {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete AI tool")

@api_router.post("/tools/{tool_id}/generate-content", response_model=ContentGenerationResponse)
async def generate_tool_content(tool_id: str):
    """Generate AI content for a specific tool"""
    try:
        # Get the tool
        tool_data = await db.ai_tools.find_one({"id": tool_id})
        if not tool_data:
            raise HTTPException(status_code=404, detail="AI tool not found")
        
        tool = AITool(**tool_data)
        
        # Update status to processing
        await db.ai_tools.update_one(
            {"id": tool_id},
            {"$set": {
                "ai_generation_status": "processing",
                "generation_status": "processing",
                "updated_at": datetime.utcnow()
            }}
        )
        
        try:
            # Generate content
            content = await ai_generator.generate_comprehensive_content(tool)
            
            # Calculate quality score
            quality_score = await ai_generator.calculate_quality_score(content)
            
            # Prepare update data
            update_data = {
                "ai_generation_status": "completed",
                "generation_status": "completed",
                "content_quality_score": quality_score,
                "quality_score": quality_score,
                "last_ai_update": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # Update with AI-generated content
            if content.get("description"):
                update_data["description"] = content["description"]
                update_data["ai_description"] = content["description"]
            
            if content.get("detailed_description"):
                update_data["detailed_description"] = content["detailed_description"]
            
            if content.get("features"):
                update_data["features"] = content["features"]
                update_data["ai_features"] = content["features"]
            
            if content.get("pricing"):
                update_data["pricing"] = content["pricing"]
                update_data["ai_pricing"] = str(content["pricing"])
            
            if content.get("pros") or content.get("cons"):
                update_data["pros_and_cons"] = ProsAndCons(
                    pros=content.get("pros", []),
                    cons=content.get("cons", [])
                ).dict()
                update_data["ai_pros"] = content.get("pros", [])
                update_data["ai_cons"] = content.get("cons", [])
            
            if content.get("use_cases"):
                update_data["ai_use_cases"] = content["use_cases"]
            
            if content.get("comparison"):
                update_data["ai_comparison"] = content["comparison"]
            
            if content.get("faqs"):
                faqs = []
                for faq_data in content["faqs"]:
                    faq = FAQ(**faq_data)
                    faqs.append(faq.dict())
                update_data["faqs"] = faqs
            
            if content.get("meta_title"):
                update_data["meta_title"] = content["meta_title"]
            
            if content.get("meta_description"):
                update_data["meta_description"] = content["meta_description"]
            
            if content.get("meta_keywords"):
                update_data["meta_keywords"] = content["meta_keywords"]
            
            if content.get("hashtags"):
                update_data["hashtags"] = content["hashtags"]
            
            if content.get("haiku"):
                update_data["haiku"] = content["haiku"]
            
            await db.ai_tools.update_one({"id": tool_id}, {"$set": update_data})
            
            return ContentGenerationResponse(
                success=True,
                message="Content generated successfully",
                tool_id=tool_id,
                quality_score=quality_score
            )
            
        except Exception as e:
            # Update status to failed
            await db.ai_tools.update_one(
                {"id": tool_id},
                {"$set": {
                    "ai_generation_status": "failed",
                    "generation_status": "failed",
                    "updated_at": datetime.utcnow()
                }}
            )
            raise e
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error generating content for tool {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate content: {str(e)}")

@api_router.post("/tools/{tool_id}/publish")
async def publish_tool(tool_id: str):
    """Publish an AI tool"""
    try:
        tool = await db.ai_tools.find_one({"id": tool_id})
        if not tool:
            raise HTTPException(status_code=404, detail="AI tool not found")
        
        await db.ai_tools.update_one(
            {"id": tool_id},
            {"$set": {
                "content_status": "published",
                "published_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }}
        )
        
        return {"message": "AI tool published successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error publishing AI tool {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to publish AI tool")

@api_router.get("/categories")
async def get_categories():
    """Get all unique categories"""
    try:
        categories = await db.ai_tools.distinct("category")
        return {"categories": [cat for cat in categories if cat]}
    except Exception as e:
        logging.error(f"Error fetching categories: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch categories")

@api_router.get("/stats")
async def get_stats():
    """Get system statistics"""
    try:
        total_tools = await db.ai_tools.count_documents({})
        completed_tools = await db.ai_tools.count_documents({"ai_generation_status": "completed"})
        published_tools = await db.ai_tools.count_documents({"content_status": "published"})
        pending_tools = await db.ai_tools.count_documents({"ai_generation_status": "pending"})
        processing_tools = await db.ai_tools.count_documents({"ai_generation_status": "processing"})
        failed_tools = await db.ai_tools.count_documents({"ai_generation_status": "failed"})
        verified_tools = await db.ai_tools.count_documents({"is_verified": True})
        
        # Calculate average quality score
        pipeline = [
            {"$match": {"content_quality_score": {"$exists": True, "$ne": None}}},
            {"$group": {"_id": None, "avg_quality": {"$avg": "$content_quality_score"}}}
        ]
        avg_quality_result = await db.ai_tools.aggregate(pipeline).to_list(1)
        avg_quality = avg_quality_result[0]["avg_quality"] if avg_quality_result else 0
        
        return {
            "total_tools": total_tools,
            "completed_tools": completed_tools,
            "published_tools": published_tools,
            "verified_tools": verified_tools,
            "pending_tools": pending_tools,
            "processing_tools": processing_tools,
            "failed_tools": failed_tools,
            "average_quality_score": round(avg_quality, 2) if avg_quality else 0,
            "automation_rate": round((completed_tools / total_tools * 100), 2) if total_tools > 0 else 0,
            "publish_rate": round((published_tools / total_tools * 100), 2) if total_tools > 0 else 0
        }
    except Exception as e:
        logging.error(f"Error fetching stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch statistics")

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    """Create indexes on startup"""
    try:
        # Create indexes for better performance
        await db.ai_tools.create_index("id", unique=True)
        await db.ai_tools.create_index("slug", unique=True)
        await db.ai_tools.create_index("category")
        await db.ai_tools.create_index("ai_generation_status")
        await db.ai_tools.create_index("content_status")
        await db.ai_tools.create_index("is_verified")
        await db.ai_tools.create_index("created_at")
        await db.ai_tools.create_index([("name", "text"), ("description", "text"), ("short_description", "text")])
        logger.info("Database indexes created successfully")
    except Exception as e:
        logger.error(f"Error creating indexes: {str(e)}")

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()