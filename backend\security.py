"""
Security utilities and middleware for DudeAI AI Agent System
"""
import re
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import HTT<PERSON>Exception, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import J<PERSON><PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel, validator
import logging

from config import settings, SECURITY_HEADERS

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token handling
security = HTTPBearer()

class TokenData(BaseModel):
    """Token data model"""
    user_id: Optional[str] = None
    scopes: List[str] = []

class SecurityMiddleware:
    """Security middleware for request validation and sanitization"""
    
    @staticmethod
    def add_security_headers(response):
        """Add security headers to response"""
        for header, value in SECURITY_HEADERS.items():
            response.headers[header] = value
        return response
    
    @staticmethod
    def validate_uuid(uuid_string: str) -> bool:
        """Validate UUID format"""
        try:
            uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not text:
            return ""
        
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\']', '', text)
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized.strip()
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """Validate URL format"""
        if not url:
            return True  # Optional URLs are allowed
        
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        if not email:
            return True  # Optional emails are allowed
        
        email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        return bool(email_pattern.match(email))

class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_tool_name(name: str) -> str:
        """Validate and sanitize tool name"""
        if not name or not name.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tool name is required"
            )
        
        sanitized = SecurityMiddleware.sanitize_string(name, 100)
        if len(sanitized) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tool name must be at least 2 characters long"
            )
        
        return sanitized
    
    @staticmethod
    def validate_description(description: str, max_length: int = 500) -> str:
        """Validate and sanitize description"""
        if not description:
            return ""
        
        sanitized = SecurityMiddleware.sanitize_string(description, max_length)
        return sanitized
    
    @staticmethod
    def validate_url_field(url: str, field_name: str) -> str:
        """Validate URL field"""
        if not url:
            return ""
        
        if not SecurityMiddleware.validate_url(url):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid {field_name} URL format"
            )
        
        if len(url) > 2048:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{field_name} URL is too long (max 2048 characters)"
            )
        
        return url
    
    @staticmethod
    def validate_category(category: str) -> str:
        """Validate category"""
        if not category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category is required"
            )
        
        allowed_categories = [
            "Text Generation", "Image Generation", "Video Generation",
            "Audio Generation", "Code Generation", "Data Analysis",
            "Chatbots", "Automation", "Design", "Marketing",
            "Productivity", "Education", "Research", "Content Creation",
            "Translation", "Summarization", "Other"
        ]
        
        if category not in allowed_categories:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(allowed_categories)}"
            )
        
        return category
    
    @staticmethod
    def validate_screenshots(screenshots: List[str]) -> List[str]:
        """Validate screenshot URLs"""
        if not screenshots:
            return []
        
        if len(screenshots) > 10:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 10 screenshots allowed"
            )
        
        validated = []
        for i, url in enumerate(screenshots):
            try:
                validated_url = InputValidator.validate_url_field(url, f"Screenshot {i+1}")
                if validated_url:
                    validated.append(validated_url)
            except HTTPException:
                # Skip invalid URLs instead of failing the entire request
                logger.warning(f"Skipping invalid screenshot URL: {url}")
                continue
        
        return validated

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials) -> TokenData:
    """Verify JWT token"""
    try:
        payload = jwt.decode(
            credentials.credentials, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        token_data = TokenData(user_id=user_id)
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return token_data

def hash_password(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password"""
    return pwd_context.verify(plain_password, hashed_password)

class RateLimitError(Exception):
    """Rate limit exceeded error"""
    pass

def sanitize_error_message(error_msg: str) -> str:
    """Sanitize error messages to prevent information disclosure"""
    # Remove sensitive patterns
    sensitive_patterns = [
        r'sk-[a-zA-Z0-9]+',  # API keys
        r'mongodb://[^/]+',  # MongoDB connection strings
        r'password[=:]\s*\S+',  # Passwords
        r'secret[=:]\s*\S+',  # Secrets
        r'token[=:]\s*\S+',  # Tokens
    ]
    
    sanitized = error_msg
    for pattern in sensitive_patterns:
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
    
    return sanitized
