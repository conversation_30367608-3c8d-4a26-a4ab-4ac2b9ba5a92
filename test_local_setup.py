#!/usr/bin/env python3
"""
Test script to verify local development setup for DudeAI AI Agent System
Tests frontend-backend communication and configuration
"""
import requests
import json
import time
import subprocess
import sys
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output"""
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_success(message):
    """Print success message"""
    print(f"{Colors.OKGREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    """Print warning message"""
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def print_error(message):
    """Print error message"""
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_header(message):
    """Print header message"""
    print(f"\n{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.BOLD} {message}{Colors.ENDC}")
    print(f"{Colors.BOLD}{'='*60}{Colors.ENDC}")

def check_backend_running():
    """Check if backend server is running"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print_success("Backend server is running on http://localhost:8000")
            return True
        else:
            print_error(f"Backend server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print_error("Backend server is not running on http://localhost:8000")
        return False
    except requests.exceptions.Timeout:
        print_error("Backend server request timed out")
        return False
    except Exception as e:
        print_error(f"Error checking backend: {e}")
        return False

def check_frontend_config():
    """Check frontend configuration"""
    frontend_env = Path("frontend/.env")
    if not frontend_env.exists():
        print_error("Frontend .env file not found")
        return False
    
    with open(frontend_env, 'r') as f:
        content = f.read()
    
    checks = [
        ("REACT_APP_API_URL=http://localhost:8000/api", "API URL points to local backend"),
        ("REACT_APP_ENVIRONMENT=development", "Environment set to development"),
        ("PORT=3000", "Frontend port set to 3000")
    ]
    
    all_passed = True
    for check, description in checks:
        if check in content:
            print_success(description)
        else:
            print_error(f"Missing or incorrect: {description}")
            all_passed = False
    
    return all_passed

def check_backend_config():
    """Check backend configuration"""
    backend_env_example = Path("backend/.env.example")
    if not backend_env_example.exists():
        print_error("Backend .env.example file not found")
        return False
    
    with open(backend_env_example, 'r') as f:
        content = f.read()
    
    checks = [
        ("ALLOWED_ORIGINS=http://localhost:3000", "CORS allows frontend origin"),
        ("MONGO_URL=", "MongoDB URL configured"),
        ("OPENAI_API_KEY=", "OpenAI API key placeholder present")
    ]
    
    all_passed = True
    for check, description in checks:
        if check in content:
            print_success(description)
        else:
            print_error(f"Missing or incorrect: {description}")
            all_passed = False
    
    return all_passed

def test_cors():
    """Test CORS configuration"""
    try:
        # Simulate a CORS preflight request
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options("http://localhost:8000/api/health", headers=headers, timeout=5)
        
        if response.status_code in [200, 204]:
            cors_headers = response.headers
            if 'Access-Control-Allow-Origin' in cors_headers:
                allowed_origin = cors_headers['Access-Control-Allow-Origin']
                if allowed_origin in ['http://localhost:3000', '*']:
                    print_success("CORS properly configured for frontend")
                    return True
                else:
                    print_error(f"CORS allows {allowed_origin}, but frontend needs http://localhost:3000")
                    return False
            else:
                print_error("CORS headers not present in response")
                return False
        else:
            print_error(f"CORS preflight request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Error testing CORS: {e}")
        return False

def test_api_endpoints():
    """Test key API endpoints"""
    endpoints = [
        ("/health", "Health check"),
        ("/stats", "Statistics"),
        ("/categories", "Categories"),
        ("/tools?page=1&page_size=5", "Tools list with pagination")
    ]
    
    all_passed = True
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8000/api{endpoint}", timeout=10)
            if response.status_code == 200:
                print_success(f"{description} endpoint working")
            else:
                print_warning(f"{description} endpoint returned status {response.status_code}")
                all_passed = False
        except Exception as e:
            print_error(f"{description} endpoint failed: {e}")
            all_passed = False
    
    return all_passed

def check_dependencies():
    """Check if required dependencies are installed"""
    print_header("Checking Dependencies")
    
    # Check Python dependencies
    try:
        import fastapi
        import uvicorn
        import motor
        import pydantic
        print_success("Backend Python dependencies installed")
    except ImportError as e:
        print_error(f"Missing backend dependency: {e}")
        return False
    
    # Check if Node.js dependencies are installed
    frontend_node_modules = Path("frontend/node_modules")
    if frontend_node_modules.exists():
        print_success("Frontend Node.js dependencies installed")
    else:
        print_error("Frontend dependencies not installed. Run 'npm install' in frontend directory.")
        return False
    
    return True

def provide_setup_instructions():
    """Provide setup instructions if tests fail"""
    print_header("Setup Instructions")
    
    print("To set up the DudeAI AI Agent System for local development:")
    print("\n1. Backend Setup:")
    print("   cd backend")
    print("   python -m venv venv")
    print("   # On Windows: venv\\Scripts\\activate")
    print("   # On Unix/Mac: source venv/bin/activate")
    print("   pip install -r requirements.txt")
    print("   cp .env.example .env")
    print("   # Edit .env with your MongoDB URL and OpenAI API key")
    
    print("\n2. Frontend Setup:")
    print("   cd frontend")
    print("   npm install")
    
    print("\n3. Start Services:")
    print("   # Terminal 1 - Backend:")
    print("   cd backend && python server.py")
    print("   # Terminal 2 - Frontend:")
    print("   cd frontend && npm start")
    
    print("\n4. Access Application:")
    print("   Frontend: http://localhost:3000")
    print("   Backend API: http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")

def main():
    """Main test function"""
    print(f"{Colors.BOLD}DudeAI AI Agent System - Local Development Setup Test{Colors.ENDC}")
    print("Testing frontend-backend communication and configuration...")
    
    all_tests_passed = True
    
    # Check dependencies
    if not check_dependencies():
        all_tests_passed = False
    
    # Check configuration files
    print_header("Checking Configuration")
    if not check_frontend_config():
        all_tests_passed = False
    
    if not check_backend_config():
        all_tests_passed = False
    
    # Check if backend is running
    print_header("Testing Backend Connection")
    backend_running = check_backend_running()
    
    if backend_running:
        # Test CORS
        print_header("Testing CORS Configuration")
        if not test_cors():
            all_tests_passed = False
        
        # Test API endpoints
        print_header("Testing API Endpoints")
        if not test_api_endpoints():
            all_tests_passed = False
    else:
        print_warning("Backend not running - skipping API tests")
        all_tests_passed = False
    
    # Summary
    print_header("Test Results")
    if all_tests_passed and backend_running:
        print_success("All tests passed! ✅")
        print_success("Frontend and backend are properly configured for local development.")
        print("\nYou can now:")
        print("• Start the frontend: cd frontend && npm start")
        print("• Access the app at: http://localhost:3000")
        print("• View API docs at: http://localhost:8000/docs")
    else:
        print_error("Some tests failed! ❌")
        if not backend_running:
            print_warning("Start the backend server first: cd backend && python server.py")
        provide_setup_instructions()

if __name__ == "__main__":
    main()
