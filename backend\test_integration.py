"""
Integration tests for DudeAI AI Agent System
"""
import pytest
import asyncio
import uuid
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from server import app
from database import db_manager
from config import settings

# Test client
client = TestClient(app)

# Test data
VALID_TOOL_DATA = {
    "name": "Test AI Tool",
    "website": "https://example.com",
    "category": "Text Generation",
    "short_description": "A test AI tool for integration testing",
    "company": "Test Company",
    "logo_url": "https://example.com/logo.png",
    "screenshots": ["https://example.com/screenshot1.png"],
    "social_links": {
        "twitter": "https://twitter.com/test",
        "github": "https://github.com/test"
    }
}

INVALID_TOOL_DATA = {
    "name": "",  # Invalid: empty name
    "website": "not-a-url",  # Invalid: malformed URL
    "category": "Invalid Category",  # Invalid: not in allowed categories
    "short_description": "x" * 1000,  # Invalid: too long
}

class TestDatabaseIntegration:
    """Test database integration"""
    
    @pytest.fixture(autouse=True)
    async def setup_database(self):
        """Setup test database"""
        # Mock database connection
        with patch.object(db_manager, 'connect', return_value=True):
            with patch.object(db_manager, 'get_collection') as mock_collection:
                mock_collection.return_value = AsyncMock()
                yield mock_collection

    def test_database_health_check(self):
        """Test database health check endpoint"""
        with patch.object(db_manager, 'health_check') as mock_health:
            mock_health.return_value = {
                "status": "healthy",
                "response_time_ms": 15.2,
                "database": "test_db"
            }
            
            response = client.get("/api/health")
            assert response.status_code == 200
            
            data = response.json()
            assert data["status"] == "healthy"
            assert "database" in data
            assert "timestamp" in data

class TestToolManagement:
    """Test tool management endpoints"""
    
    @patch.object(db_manager, 'get_collection')
    def test_create_tool_success(self, mock_get_collection):
        """Test successful tool creation"""
        mock_collection = AsyncMock()
        mock_collection.find_one.return_value = None  # No existing tool
        mock_collection.insert_one.return_value = AsyncMock()
        mock_get_collection.return_value = mock_collection
        
        response = client.post("/api/tools", json=VALID_TOOL_DATA)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == VALID_TOOL_DATA["name"]
        assert data["website"] == VALID_TOOL_DATA["website"]
        assert data["category"] == VALID_TOOL_DATA["category"]
        assert "id" in data
        assert "slug" in data
        assert "created_at" in data

    def test_create_tool_validation_errors(self):
        """Test tool creation with validation errors"""
        response = client.post("/api/tools", json=INVALID_TOOL_DATA)
        assert response.status_code == 422  # Validation error
        
        data = response.json()
        assert "detail" in data

    @patch.object(db_manager, 'get_collection')
    def test_get_tools_pagination(self, mock_get_collection):
        """Test tools listing with pagination"""
        mock_collection = AsyncMock()
        mock_get_collection.return_value = mock_collection
        
        # Mock paginated response
        with patch('server.db_operations.get_tools_with_pagination') as mock_paginate:
            mock_paginate.return_value = {
                "tools": [
                    {
                        "id": str(uuid.uuid4()),
                        "name": "Tool 1",
                        "category": "Text Generation",
                        "created_at": datetime.utcnow()
                    }
                ],
                "pagination": {
                    "page": 1,
                    "page_size": 20,
                    "total_count": 1,
                    "total_pages": 1,
                    "has_next": False,
                    "has_prev": False
                }
            }
            
            response = client.get("/api/tools?page=1&page_size=20")
            assert response.status_code == 200
            
            data = response.json()
            assert "items" in data
            assert "pagination" in data
            assert data["pagination"]["page"] == 1

    @patch.object(db_manager, 'get_collection')
    def test_get_tool_by_id(self, mock_get_collection):
        """Test getting tool by ID"""
        tool_id = str(uuid.uuid4())
        mock_collection = AsyncMock()
        mock_collection.find_one.return_value = {
            "id": tool_id,
            "name": "Test Tool",
            "category": "Text Generation",
            "created_at": datetime.utcnow()
        }
        mock_get_collection.return_value = mock_collection
        
        response = client.get(f"/api/tools/{tool_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == tool_id
        assert data["name"] == "Test Tool"

    def test_get_tool_invalid_id(self):
        """Test getting tool with invalid ID format"""
        invalid_id = "not-a-uuid"
        response = client.get(f"/api/tools/{invalid_id}")
        assert response.status_code == 400
        assert "Invalid tool ID format" in response.json()["detail"]

    @patch.object(db_manager, 'get_collection')
    def test_update_tool(self, mock_get_collection):
        """Test tool update"""
        tool_id = str(uuid.uuid4())
        mock_collection = AsyncMock()
        
        # Mock existing tool
        existing_tool = {
            "id": tool_id,
            "name": "Old Name",
            "category": "Text Generation",
            "version": 1
        }
        
        # Mock updated tool
        updated_tool = {
            **existing_tool,
            "name": "New Name",
            "version": 2
        }
        
        mock_collection.find_one.side_effect = [existing_tool, None, updated_tool]
        mock_collection.update_one.return_value = AsyncMock()
        mock_get_collection.return_value = mock_collection
        
        update_data = {"name": "New Name"}
        response = client.put(f"/api/tools/{tool_id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "New Name"

    @patch.object(db_manager, 'get_collection')
    def test_delete_tool(self, mock_get_collection):
        """Test tool deletion"""
        tool_id = str(uuid.uuid4())
        mock_collection = AsyncMock()
        mock_collection.delete_one.return_value = MagicMock(deleted_count=1)
        mock_get_collection.return_value = mock_collection
        
        response = client.delete(f"/api/tools/{tool_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert "deleted successfully" in data["message"]

class TestContentGeneration:
    """Test AI content generation"""
    
    @patch.object(db_manager, 'get_collection')
    @patch('server.ai_generator.generate_comprehensive_content')
    @patch('server.ai_generator.calculate_quality_score')
    def test_generate_content_success(self, mock_quality, mock_generate, mock_get_collection):
        """Test successful content generation"""
        tool_id = str(uuid.uuid4())
        
        # Mock tool data
        mock_collection = AsyncMock()
        mock_collection.find_one.return_value = {
            "id": tool_id,
            "name": "Test Tool",
            "category": "Text Generation"
        }
        mock_collection.update_one.return_value = AsyncMock()
        mock_get_collection.return_value = mock_collection
        
        # Mock AI generation
        mock_generate.return_value = {
            "description": "Generated description",
            "features": ["Feature 1", "Feature 2"],
            "pros": ["Pro 1", "Pro 2"],
            "cons": ["Con 1", "Con 2"]
        }
        mock_quality.return_value = 85.5
        
        response = client.post(f"/api/tools/{tool_id}/generate-content")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] == True
        assert data["quality_score"] == 85.5
        assert data["tool_id"] == tool_id

    def test_generate_content_invalid_id(self):
        """Test content generation with invalid tool ID"""
        invalid_id = "not-a-uuid"
        response = client.post(f"/api/tools/{invalid_id}/generate-content")
        assert response.status_code == 400

class TestSecurity:
    """Test security features"""
    
    def test_rate_limiting(self):
        """Test rate limiting on endpoints"""
        # Make multiple requests to trigger rate limiting
        responses = []
        for _ in range(15):  # Exceed typical rate limits
            response = client.get("/api/health")
            responses.append(response.status_code)
        
        # Should have some rate limited responses
        assert 429 in responses

    def test_input_sanitization(self):
        """Test input sanitization"""
        malicious_data = {
            "name": "<script>alert('xss')</script>Test Tool",
            "website": "https://example.com",
            "category": "Text Generation"
        }
        
        with patch.object(db_manager, 'get_collection'):
            response = client.post("/api/tools", json=malicious_data)
            
            # Should either sanitize input or reject it
            if response.status_code == 200:
                data = response.json()
                assert "<script>" not in data["name"]
            else:
                assert response.status_code in [400, 422]

    def test_sql_injection_protection(self):
        """Test protection against injection attacks"""
        injection_attempt = "'; DROP TABLE ai_tools; --"
        response = client.get(f"/api/tools/slug/{injection_attempt}")
        
        # Should handle gracefully without exposing database errors
        assert response.status_code in [400, 404]
        assert "DROP TABLE" not in response.text

class TestStatistics:
    """Test statistics endpoints"""
    
    @patch('server.db_operations.get_statistics_optimized')
    def test_get_statistics(self, mock_stats):
        """Test statistics endpoint"""
        mock_stats.return_value = {
            "total_tools": 100,
            "completed_tools": 80,
            "published_tools": 60,
            "average_quality_score": 85.5
        }
        
        response = client.get("/api/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_tools"] == 100
        assert data["completed_tools"] == 80
        assert data["average_quality_score"] == 85.5

    @patch.object(db_manager, 'get_collection')
    def test_get_categories(self, mock_get_collection):
        """Test categories endpoint"""
        mock_collection = AsyncMock()
        mock_collection.distinct.return_value = [
            "Text Generation",
            "Image Generation",
            "Video Generation"
        ]
        mock_get_collection.return_value = mock_collection
        
        response = client.get("/api/categories")
        assert response.status_code == 200
        
        data = response.json()
        assert "categories" in data
        assert len(data["categories"]) == 3

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
