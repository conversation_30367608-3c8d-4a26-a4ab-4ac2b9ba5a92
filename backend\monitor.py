#!/usr/bin/env python3
"""
Performance monitoring script for DudeAI AI Agent System
"""
import asyncio
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json

from database import db_manager, db_operations
from config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self):
        self.metrics_history = []
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'db_response_time': 1000.0,  # ms
            'api_response_time': 2000.0,  # ms
        }
    
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available
            memory_total = memory.total
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free
            disk_total = disk.total
            
            # Network metrics
            network = psutil.net_io_counters()
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'percent': memory_percent,
                    'available_bytes': memory_available,
                    'total_bytes': memory_total,
                    'used_bytes': memory_total - memory_available
                },
                'disk': {
                    'percent': disk_percent,
                    'free_bytes': disk_free,
                    'total_bytes': disk_total,
                    'used_bytes': disk_total - disk_free
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            }
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {}
    
    async def collect_database_metrics(self) -> Dict[str, Any]:
        """Collect database performance metrics"""
        try:
            start_time = time.time()
            
            # Test database health
            health = await db_manager.health_check()
            
            # Get database statistics
            stats = await db_operations.get_statistics_optimized()
            
            response_time = (time.time() - start_time) * 1000  # ms
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'health': health,
                'response_time_ms': response_time,
                'statistics': stats,
                'connection_status': 'healthy' if health['status'] == 'healthy' else 'degraded'
            }
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'health': {'status': 'error', 'error': str(e)},
                'response_time_ms': 0,
                'connection_status': 'error'
            }
    
    async def collect_application_metrics(self) -> Dict[str, Any]:
        """Collect application-specific metrics"""
        try:
            # Get tools count by status
            collection = db_manager.get_collection("ai_tools")
            
            pipeline = [
                {
                    "$group": {
                        "_id": "$ai_generation_status",
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            status_counts = {}
            async for result in collection.aggregate(pipeline):
                status_counts[result["_id"]] = result["count"]
            
            # Get recent activity (last 24 hours)
            yesterday = datetime.utcnow() - timedelta(days=1)
            recent_tools = await collection.count_documents({
                "created_at": {"$gte": yesterday}
            })
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'tools_by_status': status_counts,
                'recent_activity': {
                    'new_tools_24h': recent_tools
                },
                'total_tools': sum(status_counts.values())
            }
        except Exception as e:
            logger.error(f"Error collecting application metrics: {e}")
            return {}
    
    def check_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for performance alerts"""
        alerts = []
        
        try:
            # Check system metrics
            if 'system' in metrics:
                system = metrics['system']
                
                if system.get('cpu', {}).get('percent', 0) > self.alert_thresholds['cpu_percent']:
                    alerts.append({
                        'type': 'cpu_high',
                        'severity': 'warning',
                        'message': f"High CPU usage: {system['cpu']['percent']:.1f}%",
                        'value': system['cpu']['percent'],
                        'threshold': self.alert_thresholds['cpu_percent']
                    })
                
                if system.get('memory', {}).get('percent', 0) > self.alert_thresholds['memory_percent']:
                    alerts.append({
                        'type': 'memory_high',
                        'severity': 'warning',
                        'message': f"High memory usage: {system['memory']['percent']:.1f}%",
                        'value': system['memory']['percent'],
                        'threshold': self.alert_thresholds['memory_percent']
                    })
                
                if system.get('disk', {}).get('percent', 0) > self.alert_thresholds['disk_percent']:
                    alerts.append({
                        'type': 'disk_high',
                        'severity': 'critical',
                        'message': f"High disk usage: {system['disk']['percent']:.1f}%",
                        'value': system['disk']['percent'],
                        'threshold': self.alert_thresholds['disk_percent']
                    })
            
            # Check database metrics
            if 'database' in metrics:
                db = metrics['database']
                
                if db.get('response_time_ms', 0) > self.alert_thresholds['db_response_time']:
                    alerts.append({
                        'type': 'db_slow',
                        'severity': 'warning',
                        'message': f"Slow database response: {db['response_time_ms']:.1f}ms",
                        'value': db['response_time_ms'],
                        'threshold': self.alert_thresholds['db_response_time']
                    })
                
                if db.get('connection_status') != 'healthy':
                    alerts.append({
                        'type': 'db_connection',
                        'severity': 'critical',
                        'message': f"Database connection issue: {db['connection_status']}",
                        'value': db['connection_status']
                    })
        
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
        
        return alerts
    
    async def run_monitoring_cycle(self) -> Dict[str, Any]:
        """Run a complete monitoring cycle"""
        logger.info("Starting monitoring cycle...")
        
        # Collect all metrics
        system_metrics = await self.collect_system_metrics()
        database_metrics = await self.collect_database_metrics()
        application_metrics = await self.collect_application_metrics()
        
        # Combine metrics
        all_metrics = {
            'timestamp': datetime.utcnow().isoformat(),
            'system': system_metrics,
            'database': database_metrics,
            'application': application_metrics
        }
        
        # Check for alerts
        alerts = self.check_alerts(all_metrics)
        all_metrics['alerts'] = alerts
        
        # Store in history
        self.metrics_history.append(all_metrics)
        
        # Keep only last 100 entries
        if len(self.metrics_history) > 100:
            self.metrics_history = self.metrics_history[-100:]
        
        # Log alerts
        for alert in alerts:
            if alert['severity'] == 'critical':
                logger.error(f"CRITICAL ALERT: {alert['message']}")
            else:
                logger.warning(f"WARNING ALERT: {alert['message']}")
        
        logger.info(f"Monitoring cycle completed. {len(alerts)} alerts generated.")
        return all_metrics
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of recent metrics"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        
        return {
            'latest_metrics': latest,
            'history_count': len(self.metrics_history),
            'alerts_summary': {
                'total_alerts': len(latest.get('alerts', [])),
                'critical_alerts': len([a for a in latest.get('alerts', []) if a['severity'] == 'critical']),
                'warning_alerts': len([a for a in latest.get('alerts', []) if a['severity'] == 'warning'])
            }
        }

async def main():
    """Main monitoring function"""
    monitor = PerformanceMonitor()
    
    # Connect to database
    connected = await db_manager.connect()
    if not connected:
        logger.error("Failed to connect to database")
        return
    
    try:
        # Run monitoring cycle
        metrics = await monitor.run_monitoring_cycle()
        
        # Print summary
        summary = monitor.get_metrics_summary()
        print(json.dumps(summary, indent=2, default=str))
        
    except Exception as e:
        logger.error(f"Monitoring failed: {e}")
    finally:
        await db_manager.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
