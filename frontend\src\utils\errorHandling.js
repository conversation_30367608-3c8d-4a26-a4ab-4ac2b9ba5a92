/**
 * Error handling utilities for DudeAI AI Agent System
 */

// <PERSON>rror types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTH<PERSON><PERSON><PERSON>ATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  SERVER: 'SERVER_ERROR',
  RATE_LIMIT: 'RATE_LIMIT_ERROR',
  TIMEOUT: 'TIMEOUT_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Parse error response and categorize error type
 */
export const parseError = (error) => {
  const defaultError = {
    type: ERROR_TYPES.UNKNOWN,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'An unexpected error occurred',
    details: null,
    retryable: false
  };

  if (!error) {
    return defaultError;
  }

  // Network errors
  if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
    return {
      type: ERROR_TYPES.NETWORK,
      severity: ERROR_SEVERITY.HIGH,
      message: 'Network connection failed. Please check your internet connection.',
      details: error.message,
      retryable: true
    };
  }

  // Timeout errors
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    return {
      type: ERROR_TYPES.TIMEOUT,
      severity: ERROR_SEVERITY.MEDIUM,
      message: 'Request timed out. Please try again.',
      details: error.message,
      retryable: true
    };
  }

  // HTTP response errors
  if (error.response) {
    const status = error.response.status;
    const data = error.response.data;

    switch (status) {
      case 400:
        return {
          type: ERROR_TYPES.VALIDATION,
          severity: ERROR_SEVERITY.LOW,
          message: data?.detail || 'Invalid request data',
          details: data,
          retryable: false
        };

      case 401:
        return {
          type: ERROR_TYPES.AUTHENTICATION,
          severity: ERROR_SEVERITY.HIGH,
          message: 'Authentication required',
          details: data,
          retryable: false
        };

      case 403:
        return {
          type: ERROR_TYPES.AUTHORIZATION,
          severity: ERROR_SEVERITY.HIGH,
          message: 'Access denied',
          details: data,
          retryable: false
        };

      case 404:
        return {
          type: ERROR_TYPES.NOT_FOUND,
          severity: ERROR_SEVERITY.LOW,
          message: 'Resource not found',
          details: data,
          retryable: false
        };

      case 408:
        return {
          type: ERROR_TYPES.TIMEOUT,
          severity: ERROR_SEVERITY.MEDIUM,
          message: 'Request timed out',
          details: data,
          retryable: true
        };

      case 422:
        return {
          type: ERROR_TYPES.VALIDATION,
          severity: ERROR_SEVERITY.LOW,
          message: 'Validation failed',
          details: data,
          retryable: false
        };

      case 429:
        return {
          type: ERROR_TYPES.RATE_LIMIT,
          severity: ERROR_SEVERITY.MEDIUM,
          message: 'Too many requests. Please wait and try again.',
          details: data,
          retryable: true
        };

      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: ERROR_TYPES.SERVER,
          severity: ERROR_SEVERITY.HIGH,
          message: 'Server error. Please try again later.',
          details: data,
          retryable: true
        };

      default:
        return {
          type: ERROR_TYPES.UNKNOWN,
          severity: ERROR_SEVERITY.MEDIUM,
          message: data?.detail || `HTTP ${status} error`,
          details: data,
          retryable: status >= 500
        };
    }
  }

  // JavaScript errors
  if (error instanceof Error) {
    return {
      type: ERROR_TYPES.UNKNOWN,
      severity: ERROR_SEVERITY.MEDIUM,
      message: error.message || 'An error occurred',
      details: {
        name: error.name,
        stack: error.stack
      },
      retryable: false
    };
  }

  return defaultError;
};

/**
 * Sanitize user input to prevent XSS
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') {
    return input;
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Validate URL format
 */
export const isValidUrl = (url) => {
  if (!url) return true; // Allow empty URLs

  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Validate email format
 */
export const isValidEmail = (email) => {
  if (!email) return true; // Allow empty emails

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Debounce function to limit API calls
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000) => {
  let lastError;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      const parsedError = parseError(error);
      
      // Don't retry if error is not retryable
      if (!parsedError.retryable) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries - 1) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Log error to console with additional context
 */
export const logError = (error, context = {}) => {
  const parsedError = parseError(error);
  
  const logData = {
    timestamp: new Date().toISOString(),
    error: parsedError,
    context,
    userAgent: navigator.userAgent,
    url: window.location.href
  };

  if (process.env.NODE_ENV === 'development') {
    console.error('Error logged:', logData);
  }

  // In production, you might want to send this to an error tracking service
  // Example: sendToErrorTrackingService(logData);
};

/**
 * Format error message for user display
 */
export const formatErrorMessage = (error) => {
  const parsedError = parseError(error);
  
  // Return user-friendly message
  return parsedError.message;
};

/**
 * Check if error should show retry button
 */
export const shouldShowRetry = (error) => {
  const parsedError = parseError(error);
  return parsedError.retryable;
};

/**
 * Get error color for UI display
 */
export const getErrorColor = (error) => {
  const parsedError = parseError(error);
  
  switch (parsedError.severity) {
    case ERROR_SEVERITY.LOW:
      return 'yellow';
    case ERROR_SEVERITY.MEDIUM:
      return 'orange';
    case ERROR_SEVERITY.HIGH:
      return 'red';
    case ERROR_SEVERITY.CRITICAL:
      return 'red';
    default:
      return 'gray';
  }
};

/**
 * Create error handler for React components
 */
export const createErrorHandler = (setError, context = {}) => {
  return (error) => {
    logError(error, context);
    setError(error);
  };
};

/**
 * Validate form data
 */
export const validateFormData = (data, rules) => {
  const errors = {};

  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field];

    if (fieldRules.required && (!value || value.toString().trim() === '')) {
      errors[field] = `${field} is required`;
      continue;
    }

    if (value && fieldRules.minLength && value.length < fieldRules.minLength) {
      errors[field] = `${field} must be at least ${fieldRules.minLength} characters`;
      continue;
    }

    if (value && fieldRules.maxLength && value.length > fieldRules.maxLength) {
      errors[field] = `${field} must be no more than ${fieldRules.maxLength} characters`;
      continue;
    }

    if (value && fieldRules.pattern && !fieldRules.pattern.test(value)) {
      errors[field] = fieldRules.message || `${field} format is invalid`;
      continue;
    }

    if (value && fieldRules.url && !isValidUrl(value)) {
      errors[field] = `${field} must be a valid URL`;
      continue;
    }

    if (value && fieldRules.email && !isValidEmail(value)) {
      errors[field] = `${field} must be a valid email`;
      continue;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
