{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.8.4", "cra-template": "1.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@eslint/js": "9.23.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "autoprefixer": "^10.4.20", "eslint": "9.23.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.4", "globals": "15.15.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}