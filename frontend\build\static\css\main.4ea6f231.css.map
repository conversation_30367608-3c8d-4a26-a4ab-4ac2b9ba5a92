{"version": 3, "file": "static/css/main.4ea6f231.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,qBAAmB,CAAnB,mBAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,yCAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,oEAAmB,CAAnB,mEAAmB,CAAnB,sEAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,yBAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,sBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,yBAAmB,CAAnB,8LAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAEnB,KAKI,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEgC,CAHhC,QAMJ,CAEA,KACI,uEAEJ,CAhBA,mDAiBA,CAjBA,oBAiBA,CAjBA,wDAiBA,CAjBA,mDAiBA,CAjBA,oBAiBA,CAjBA,wDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,sDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,wDAiBA,CAjBA,uFAiBA,CAjBA,yDAiBA,CAjBA,iEAiBA,CAjBA,wFAiBA,CAjBA,yDAiBA,CAjBA,iEAiBA,CAjBA,iFAiBA,CAjBA,mFAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,6CAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,4CAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,4CAiBA,CAjBA,gDAiBA,CAjBA,aAiBA,CAjBA,6CAiBA,CAjBA,8CAiBA,CAjBA,aAiBA,CAjBA,6CAiBA,CAjBA,wFAiBA,CAjBA,kGAiBA,CAjBA,+CAiBA,CAjBA,kGAiBA,CAjBA,mDAiBA,CAjBA,kDAiBA,CAjBA,kBAiBA,CAjBA,+HAiBA,CAjBA,wGAiBA,CAjBA,uEAiBA,CAjBA,wFAiBA,CAjBA,+CAiBA,CAjBA,wDAiBA,CAjBA,+CAiBA,CAjBA,yDAiBA,CAjBA,yCAiBA,CAjBA,8CAiBA,CAjBA,8DAiBA,CAjBA,8DAiBA,CAjBA,gCAiBA,EAjBA,wFAiBA,ECjBA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YACE,wBAAyB,CAEzB,UAAY,CADZ,YAEF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,SACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,OACE,2BACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,eAIE,6BAAoC,CAHpC,iDAAoD,CACpD,oBAAqB,CACrB,4BAEF,CAGA,SAME,iCAAkC,CAJlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAK7B,oBAAqB,CAFrB,WAAY,CAGZ,gBAAiB,CAJjB,UAKF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,mBACE,qBAAsB,CACtB,oCACF,CAEA,oBACE,gCAA0C,CAC1C,qCACF,CAGA,kBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,eACE,wBAAyB,CACzB,aACF,CAEA,gBACE,wBAAyB,CACzB,aACF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,yBACE,iBACE,yBACF,CAEA,iBACE,iBACF,CACF,CAGA,kBAEE,QAAS,CACT,kBAAmB,CACnB,kBAAyB,CAHzB,YAIF,CAGA,iBAEE,+BAA0C,CAD1C,0BAA2B,CAE3B,8BACF,CAEA,kBACE,uBACF,CAGA,kBAEE,+BAAyC,CADzC,0BAA2B,CAE3B,8BACF,CAGA,mBACE,aAAc,CACd,eACF,CAEA,cACE,aAAc,CACd,eACF,CAEA,cACE,aAAc,CACd,eACF,CAGA,iBAGE,6BAA8B,CAF9B,qEAAyE,CACzE,yBAEF,CAEA,mBACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAGA,eAEE,wBAAyB,CADzB,6BAA8B,CAE9B,aACF,CAEA,aAEE,wBAAyB,CADzB,6BAA8B,CAE9B,aACF,CAGA,eAIE,6BAAoC,CAHpC,kDAA6D,CAC7D,oBAAqB,CACrB,4BAEF,CAGA,kBAGE,4BAA6B,CAF7B,uBAA8B,CAC9B,kBAEF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\nbody {\r\n    margin: 0;\r\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\",\r\n        \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\",\r\n        \"Helvetica Neue\", sans-serif;\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n    font-family: source-code-pro, Menlo, Monaco, Consolas, \"Courier New\",\r\n        monospace;\r\n}\r\n", ".App {\r\n  text-align: center;\r\n}\r\n\r\n.App-logo {\r\n  height: 40vmin;\r\n  pointer-events: none;\r\n}\r\n\r\n@media (prefers-reduced-motion: no-preference) {\r\n  .App-logo {\r\n    animation: App-logo-spin infinite 20s linear;\r\n  }\r\n}\r\n\r\n.App-header {\r\n  background-color: #282c34;\r\n  padding: 20px;\r\n  color: white;\r\n}\r\n\r\n.App-link {\r\n  color: #61dafb;\r\n}\r\n\r\n@keyframes App-logo-spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Custom animations and enhancements */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.pulse {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n}\r\n\r\n.gradient-text {\r\n  background: linear-gradient(45deg, #3b82f6, #8b5cf6);\r\n  background-clip: text;\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n}\r\n\r\n/* Loading spinner */\r\n.spinner {\r\n  border: 4px solid #f3f3f3;\r\n  border-top: 4px solid #3498db;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  animation: spin 1s linear infinite;\r\n  display: inline-block;\r\n  margin-right: 8px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Hover effects */\r\n.hover-scale:hover {\r\n  transform: scale(1.02);\r\n  transition: transform 0.2s ease-in-out;\r\n}\r\n\r\n.hover-shadow:hover {\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  transition: box-shadow 0.3s ease-in-out;\r\n}\r\n\r\n/* Status indicators */\r\n.status-completed {\r\n  background-color: #dcfce7;\r\n  color: #166534;\r\n}\r\n\r\n.status-processing {\r\n  background-color: #fef3c7;\r\n  color: #92400e;\r\n}\r\n\r\n.status-failed {\r\n  background-color: #fee2e2;\r\n  color: #991b1b;\r\n}\r\n\r\n.status-pending {\r\n  background-color: #f3f4f6;\r\n  color: #374151;\r\n}\r\n\r\n/* Custom scrollbar */\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* Responsive design improvements */\r\n@media (max-width: 768px) {\r\n  .grid-responsive {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .text-responsive {\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n\r\n/* Form styling */\r\n.form-input:focus {\r\n  outline: none;\r\n  ring: 2px;\r\n  ring-color: #3b82f6;\r\n  border-color: transparent;\r\n}\r\n\r\n/* Button animations */\r\n.btn-hover:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transition: all 0.2s ease-in-out;\r\n}\r\n\r\n.btn-hover:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n/* Card enhancements */\r\n.card-hover:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n/* Quality score styling */\r\n.quality-excellent {\r\n  color: #059669;\r\n  font-weight: bold;\r\n}\r\n\r\n.quality-good {\r\n  color: #d97706;\r\n  font-weight: bold;\r\n}\r\n\r\n.quality-poor {\r\n  color: #dc2626;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Loading states */\r\n.loading-shimmer {\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n/* Success/Error alerts */\r\n.alert-success {\r\n  border-left: 4px solid #10b981;\r\n  background-color: #d1fae5;\r\n  color: #065f46;\r\n}\r\n\r\n.alert-error {\r\n  border-left: 4px solid #ef4444;\r\n  background-color: #fee2e2;\r\n  color: #991b1b;\r\n}\r\n\r\n/* Typography improvements */\r\n.text-gradient {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  background-clip: text;\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n}\r\n\r\n/* Enhanced focus states */\r\n.focus-ring:focus {\r\n  outline: 2px solid transparent;\r\n  outline-offset: 2px;\r\n  box-shadow: 0 0 0 2px #3b82f6;\r\n}"], "names": [], "sourceRoot": ""}