#!/usr/bin/env python3
"""
Comprehensive test runner for DudeAI AI Agent System
"""
import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from datetime import datetime

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message, color=Colors.OKBLUE):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.ENDC}")

def print_header(message):
    """Print header message"""
    print_colored(f"\n{'='*60}", Colors.HEADER)
    print_colored(f" {message}", Colors.HEADER)
    print_colored(f"{'='*60}", Colors.HEADER)

def print_success(message):
    """Print success message"""
    print_colored(f"✓ {message}", Colors.OKGREEN)

def print_warning(message):
    """Print warning message"""
    print_colored(f"⚠ {message}", Colors.WARNING)

def print_error(message):
    """Print error message"""
    print_colored(f"✗ {message}", Colors.FAIL)

def run_command(command, cwd=None, capture_output=True):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=capture_output,
            text=True,
            timeout=300  # 5 minute timeout
        )
        return result
    except subprocess.TimeoutExpired:
        print_error(f"Command timed out: {command}")
        return None
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {command}")
        return e

class TestRunner:
    """Main test runner class"""
    
    def __init__(self):
        self.results = {
            'backend': {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': []},
            'frontend': {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': []},
            'integration': {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': []},
            'security': {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': []},
            'performance': {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': []}
        }
        self.start_time = None
        self.end_time = None
    
    def run_backend_tests(self, test_type='all'):
        """Run backend tests"""
        print_header("Running Backend Tests")
        
        backend_dir = Path("backend")
        if not backend_dir.exists():
            print_error("Backend directory not found")
            return False
        
        # Determine Python command
        if os.name == 'nt':
            python_cmd = "venv\\Scripts\\python"
            pytest_cmd = "venv\\Scripts\\pytest"
        else:
            python_cmd = "venv/bin/python"
            pytest_cmd = "venv/bin/pytest"
        
        # Check if virtual environment exists
        venv_dir = backend_dir / "venv"
        if not venv_dir.exists():
            print_error("Virtual environment not found. Run setup.py first.")
            return False
        
        test_commands = []
        
        if test_type in ['all', 'unit']:
            test_commands.append({
                'name': 'Unit Tests',
                'command': f"{pytest_cmd} test_security.py -v --tb=short"
            })
        
        if test_type in ['all', 'integration']:
            test_commands.append({
                'name': 'Integration Tests',
                'command': f"{pytest_cmd} test_integration.py -v --tb=short"
            })
        
        if test_type in ['all', 'coverage']:
            test_commands.append({
                'name': 'Coverage Report',
                'command': f"{pytest_cmd} --cov=. --cov-report=html --cov-report=term"
            })
        
        success = True
        for test in test_commands:
            print_colored(f"\nRunning {test['name']}...")
            result = run_command(test['command'], cwd=backend_dir)
            
            if result and result.returncode == 0:
                print_success(f"{test['name']} passed")
                self.results['backend']['passed'] += 1
            else:
                print_error(f"{test['name']} failed")
                self.results['backend']['failed'] += 1
                if result:
                    self.results['backend']['errors'].append({
                        'test': test['name'],
                        'error': result.stderr or result.stdout
                    })
                success = False
        
        return success
    
    def run_frontend_tests(self):
        """Run frontend tests"""
        print_header("Running Frontend Tests")
        
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print_error("Frontend directory not found")
            return False
        
        # Check if node_modules exists
        node_modules = frontend_dir / "node_modules"
        if not node_modules.exists():
            print_error("Node modules not found. Run 'npm install' in frontend directory.")
            return False
        
        test_commands = [
            {
                'name': 'React Component Tests',
                'command': 'npm test -- --watchAll=false --coverage --testResultsProcessor=jest-sonar-reporter'
            },
            {
                'name': 'ESLint Code Quality',
                'command': 'npm run lint'
            }
        ]
        
        success = True
        for test in test_commands:
            print_colored(f"\nRunning {test['name']}...")
            result = run_command(test['command'], cwd=frontend_dir)
            
            if result and result.returncode == 0:
                print_success(f"{test['name']} passed")
                self.results['frontend']['passed'] += 1
            else:
                print_error(f"{test['name']} failed")
                self.results['frontend']['failed'] += 1
                if result:
                    self.results['frontend']['errors'].append({
                        'test': test['name'],
                        'error': result.stderr or result.stdout
                    })
                success = False
        
        return success
    
    def run_security_tests(self):
        """Run security tests"""
        print_header("Running Security Tests")
        
        backend_dir = Path("backend")
        
        # Determine Python command
        if os.name == 'nt':
            python_cmd = "venv\\Scripts\\python"
        else:
            python_cmd = "venv/bin/python"
        
        security_tests = [
            {
                'name': 'Security Unit Tests',
                'command': f"{python_cmd} -m pytest test_security.py::TestSecurity -v"
            },
            {
                'name': 'Input Validation Tests',
                'command': f"{python_cmd} -m pytest test_security.py::TestInputValidator -v"
            },
            {
                'name': 'Rate Limiting Tests',
                'command': f"{python_cmd} -m pytest test_security.py::TestRateLimiting -v"
            }
        ]
        
        success = True
        for test in security_tests:
            print_colored(f"\nRunning {test['name']}...")
            result = run_command(test['command'], cwd=backend_dir)
            
            if result and result.returncode == 0:
                print_success(f"{test['name']} passed")
                self.results['security']['passed'] += 1
            else:
                print_error(f"{test['name']} failed")
                self.results['security']['failed'] += 1
                if result:
                    self.results['security']['errors'].append({
                        'test': test['name'],
                        'error': result.stderr or result.stdout
                    })
                success = False
        
        return success
    
    def run_performance_tests(self):
        """Run performance tests"""
        print_header("Running Performance Tests")
        
        backend_dir = Path("backend")
        
        # Determine Python command
        if os.name == 'nt':
            python_cmd = "venv\\Scripts\\python"
        else:
            python_cmd = "venv/bin/python"
        
        print_colored("Running performance monitoring...")
        result = run_command(f"{python_cmd} monitor.py", cwd=backend_dir)
        
        if result and result.returncode == 0:
            print_success("Performance monitoring completed")
            self.results['performance']['passed'] += 1
            return True
        else:
            print_error("Performance monitoring failed")
            self.results['performance']['failed'] += 1
            if result:
                self.results['performance']['errors'].append({
                    'test': 'Performance Monitoring',
                    'error': result.stderr or result.stdout
                })
            return False
    
    def run_integration_tests(self):
        """Run integration tests"""
        print_header("Running Integration Tests")
        
        # Run the existing backend test
        print_colored("Running API integration tests...")
        result = run_command("python backend_test.py")
        
        if result and result.returncode == 0:
            print_success("Integration tests passed")
            self.results['integration']['passed'] += 1
            return True
        else:
            print_error("Integration tests failed")
            self.results['integration']['failed'] += 1
            if result:
                self.results['integration']['errors'].append({
                    'test': 'API Integration Tests',
                    'error': result.stderr or result.stdout
                })
            return False
    
    def generate_report(self):
        """Generate test report"""
        print_header("Test Results Summary")
        
        total_passed = sum(category['passed'] for category in self.results.values())
        total_failed = sum(category['failed'] for category in self.results.values())
        total_tests = total_passed + total_failed
        
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        print_colored(f"Total Tests: {total_tests}")
        print_colored(f"Passed: {total_passed}", Colors.OKGREEN)
        print_colored(f"Failed: {total_failed}", Colors.FAIL if total_failed > 0 else Colors.OKGREEN)
        print_colored(f"Duration: {duration:.2f} seconds")
        
        # Detailed results by category
        for category, results in self.results.items():
            if results['passed'] > 0 or results['failed'] > 0:
                print_colored(f"\n{category.title()}:")
                print_colored(f"  Passed: {results['passed']}", Colors.OKGREEN)
                if results['failed'] > 0:
                    print_colored(f"  Failed: {results['failed']}", Colors.FAIL)
                    for error in results['errors']:
                        print_colored(f"    - {error['test']}", Colors.FAIL)
        
        # Generate JSON report
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration_seconds': duration,
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failed': total_failed,
                'success_rate': (total_passed / total_tests * 100) if total_tests > 0 else 0
            },
            'results': self.results
        }
        
        report_file = Path("test_report.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print_colored(f"\nDetailed report saved to: {report_file}")
        
        return total_failed == 0

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run DudeAI AI Agent System tests")
    parser.add_argument("--backend", action="store_true", help="Run backend tests only")
    parser.add_argument("--frontend", action="store_true", help="Run frontend tests only")
    parser.add_argument("--security", action="store_true", help="Run security tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", help="Include coverage report")
    parser.add_argument("--fast", action="store_true", help="Run fast tests only (skip integration)")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    runner.start_time = datetime.now()
    
    print_colored("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    DudeAI Test Runner                        ║
    ╚══════════════════════════════════════════════════════════════╝
    """, Colors.HEADER)
    
    success = True
    
    try:
        if args.backend or not any([args.frontend, args.security, args.performance, args.integration]):
            test_type = 'coverage' if args.coverage else 'all'
            success &= runner.run_backend_tests(test_type)
        
        if args.frontend or not any([args.backend, args.security, args.performance, args.integration]):
            success &= runner.run_frontend_tests()
        
        if args.security or not any([args.backend, args.frontend, args.performance, args.integration]):
            success &= runner.run_security_tests()
        
        if args.performance:
            success &= runner.run_performance_tests()
        
        if (args.integration or not any([args.backend, args.frontend, args.security, args.performance])) and not args.fast:
            success &= runner.run_integration_tests()
        
    except KeyboardInterrupt:
        print_error("\nTests interrupted by user")
        success = False
    
    runner.end_time = datetime.now()
    overall_success = runner.generate_report()
    
    if overall_success and success:
        print_success("\nAll tests passed! 🎉")
        sys.exit(0)
    else:
        print_error("\nSome tests failed! 😞")
        sys.exit(1)

if __name__ == "__main__":
    main()
