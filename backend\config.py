"""
Configuration and security settings for DudeAI AI Agent System
"""
import os
import secrets
from typing import List, Optional
from pydantic import BaseSettings, validator
from pathlib import Path

class Settings(BaseSettings):
    """Application settings with validation"""
    
    # Database settings
    MONGO_URL: str
    DB_NAME: str
    
    # Security settings
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # API settings
    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "DudeAI AI Agent System"
    VERSION: str = "1.0.0"
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    ALLOWED_HEADERS: List[str] = ["*"]
    
    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds
    
    # Request limits
    MAX_REQUEST_SIZE: int = 10 * 1024 * 1024  # 10MB
    MAX_CONTENT_LENGTH: int = 1024 * 1024  # 1MB for content generation
    
    # AI settings
    OPENAI_API_KEY: str
    AI_GENERATION_TIMEOUT: int = 300  # 5 minutes
    MAX_TOKENS: int = 3000
    
    # Cache settings
    REDIS_URL: Optional[str] = None
    CACHE_TTL: int = 300  # 5 minutes
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    
    @validator("MONGO_URL")
    def validate_mongo_url(cls, v):
        if not v:
            raise ValueError("MONGO_URL is required")
        if not v.startswith(("mongodb://", "mongodb+srv://")):
            raise ValueError("MONGO_URL must be a valid MongoDB connection string")
        return v
    
    @validator("DB_NAME")
    def validate_db_name(cls, v):
        if not v:
            raise ValueError("DB_NAME is required")
        if len(v) < 1 or len(v) > 64:
            raise ValueError("DB_NAME must be between 1 and 64 characters")
        return v
    
    @validator("OPENAI_API_KEY")
    def validate_openai_key(cls, v):
        if not v:
            raise ValueError("OPENAI_API_KEY is required")
        if not v.startswith("sk-"):
            raise ValueError("OPENAI_API_KEY must be a valid OpenAI API key")
        return v
    
    @validator("ALLOWED_ORIGINS")
    def validate_origins(cls, v):
        if not v:
            return ["http://localhost:3000"]
        return v
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("ENVIRONMENT must be one of: development, staging, production")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Security constants
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
}

# Quality scoring constants
QUALITY_SCORE_WEIGHTS = {
    "DESCRIPTION_WEIGHT": 25,
    "DETAILED_DESCRIPTION_WEIGHT": 15,
    "FEATURES_WEIGHT": 15,
    "PRICING_WEIGHT": 10,
    "PROS_CONS_WEIGHT": 15,
    "USE_CASES_WEIGHT": 10,
    "FAQS_WEIGHT": 5,
    "SEO_WEIGHT": 3,
    "CREATIVE_WEIGHT": 2,
    "MIN_DESCRIPTION_WORDS": 50,
    "GOOD_DESCRIPTION_WORDS": 100,
    "MIN_DETAILED_WORDS": 100,
    "GOOD_DETAILED_WORDS": 200,
    "MIN_FEATURES": 3,
    "GOOD_FEATURES": 8,
    "MIN_PROS_CONS": 2,
    "GOOD_PROS_CONS": 4,
    "MIN_USE_CASES": 2,
    "GOOD_USE_CASES": 6,
    "MIN_FAQS": 3,
    "GOOD_FAQS": 5
}

# Database pagination constants
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Content validation constants
MAX_NAME_LENGTH = 100
MAX_DESCRIPTION_LENGTH = 500
MAX_DETAILED_DESCRIPTION_LENGTH = 5000
MAX_URL_LENGTH = 2048
MAX_FEATURES_COUNT = 20
MAX_FAQS_COUNT = 20
MAX_TAGS_COUNT = 10

def get_settings() -> Settings:
    """Get application settings"""
    return Settings()

# Global settings instance
settings = get_settings()
