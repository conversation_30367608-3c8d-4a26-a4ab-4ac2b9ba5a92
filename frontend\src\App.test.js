import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from './App';

// Mock fetch globally
global.fetch = jest.fn();

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

beforeEach(() => {
  fetch.mockClear();
  console.error = jest.fn();
  console.log = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.log = originalConsoleLog;
});

describe('App Component', () => {
  test('renders main heading', () => {
    render(<App />);
    const heading = screen.getByText(/DudeAI AI Agent System/i);
    expect(heading).toBeInTheDocument();
  });

  test('renders add tool form', () => {
    render(<App />);
    const nameInput = screen.getByPlaceholderText(/Tool Name/i);
    const websiteInput = screen.getByPlaceholderText(/Website URL/i);
    const addButton = screen.getByText(/Add Tool/i);
    
    expect(nameInput).toBeInTheDocument();
    expect(websiteInput).toBeInTheDocument();
    expect(addButton).toBeInTheDocument();
  });

  test('validates required fields when adding tool', async () => {
    render(<App />);
    const addButton = screen.getByText(/Add Tool/i);
    
    fireEvent.click(addButton);
    
    // Should show validation error for empty name
    await waitFor(() => {
      expect(console.error).toHaveBeenCalled();
    });
  });

  test('adds tool with valid data', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '123',
        name: 'Test Tool',
        website: 'https://example.com',
        category: 'Text Generation'
      })
    });

    render(<App />);
    
    const nameInput = screen.getByPlaceholderText(/Tool Name/i);
    const websiteInput = screen.getByPlaceholderText(/Website URL/i);
    const categorySelect = screen.getByDisplayValue(/Select Category/i);
    const addButton = screen.getByText(/Add Tool/i);
    
    fireEvent.change(nameInput, { target: { value: 'Test Tool' } });
    fireEvent.change(websiteInput, { target: { value: 'https://example.com' } });
    fireEvent.change(categorySelect, { target: { value: 'Text Generation' } });
    
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/tools'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('Test Tool')
        })
      );
    });
  });

  test('handles API errors gracefully', async () => {
    fetch.mockRejectedValueOnce(new Error('API Error'));

    render(<App />);
    
    const nameInput = screen.getByPlaceholderText(/Tool Name/i);
    const addButton = screen.getByText(/Add Tool/i);
    
    fireEvent.change(nameInput, { target: { value: 'Test Tool' } });
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Error adding tool'),
        expect.any(Error)
      );
    });
  });

  test('loads tools on component mount', async () => {
    const mockTools = {
      items: [
        {
          id: '1',
          name: 'Tool 1',
          category: 'Text Generation',
          website: 'https://example1.com'
        },
        {
          id: '2',
          name: 'Tool 2',
          category: 'Image Generation',
          website: 'https://example2.com'
        }
      ],
      pagination: {
        page: 1,
        total_pages: 1,
        total_count: 2
      }
    };

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockTools
    });

    render(<App />);
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/tools'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });
  });

  test('filters tools by category', async () => {
    const mockTools = {
      items: [
        {
          id: '1',
          name: 'Text Tool',
          category: 'Text Generation',
          website: 'https://example1.com'
        }
      ],
      pagination: { page: 1, total_pages: 1, total_count: 1 }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: async () => mockTools
    });

    render(<App />);
    
    const categoryFilter = screen.getByDisplayValue(/All Categories/i);
    fireEvent.change(categoryFilter, { target: { value: 'Text Generation' } });
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('category=Text%20Generation'),
        expect.any(Object)
      );
    });
  });

  test('searches tools by name', async () => {
    const mockTools = {
      items: [],
      pagination: { page: 1, total_pages: 1, total_count: 0 }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: async () => mockTools
    });

    render(<App />);
    
    const searchInput = screen.getByPlaceholderText(/Search tools/i);
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    // Wait for debounced search
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=test%20search'),
        expect.any(Object)
      );
    }, { timeout: 1500 });
  });

  test('generates content for tool', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        message: 'Content generated successfully',
        quality_score: 85
      })
    });

    render(<App />);
    
    // Mock a tool being present
    const generateButton = screen.getByText(/Generate Content/i);
    fireEvent.click(generateButton);
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/generate-content'),
        expect.objectContaining({
          method: 'POST'
        })
      );
    });
  });

  test('handles pagination', async () => {
    const mockTools = {
      items: [],
      pagination: {
        page: 1,
        total_pages: 3,
        total_count: 60,
        has_next: true,
        has_prev: false
      }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: async () => mockTools
    });

    render(<App />);
    
    await waitFor(() => {
      const nextButton = screen.getByText(/Next/i);
      expect(nextButton).toBeInTheDocument();
      expect(nextButton).not.toBeDisabled();
    });
  });

  test('validates URL format', () => {
    render(<App />);
    
    const websiteInput = screen.getByPlaceholderText(/Website URL/i);
    fireEvent.change(websiteInput, { target: { value: 'invalid-url' } });
    fireEvent.blur(websiteInput);
    
    // Should show validation error for invalid URL
    expect(websiteInput.value).toBe('invalid-url');
  });

  test('sanitizes user input', () => {
    render(<App />);
    
    const nameInput = screen.getByPlaceholderText(/Tool Name/i);
    const maliciousInput = '<script>alert("xss")</script>Test Tool';
    
    fireEvent.change(nameInput, { target: { value: maliciousInput } });
    
    // Input should be sanitized
    expect(nameInput.value).not.toContain('<script>');
  });

  test('displays loading states', async () => {
    // Mock a slow API response
    fetch.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ items: [], pagination: {} })
        }), 100)
      )
    );

    render(<App />);
    
    // Should show loading indicator
    expect(screen.getByText(/Loading/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText(/Loading/i)).not.toBeInTheDocument();
    });
  });

  test('handles network errors', async () => {
    fetch.mockRejectedValue(new Error('Network error'));

    render(<App />);
    
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Error loading tools'),
        expect.any(Error)
      );
    });
  });
});
