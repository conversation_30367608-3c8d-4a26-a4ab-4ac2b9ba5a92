#!/usr/bin/env python3
"""
Setup script for DudeAI AI Agent System
Automates the installation and configuration process
"""
import os
import sys
import subprocess
import shutil
import secrets
from pathlib import Path
import argparse
import json

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_colored(message, color=Colors.OKBLUE):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.ENDC}")

def print_header(message):
    """Print header message"""
    print_colored(f"\n{'='*60}", Colors.HEADER)
    print_colored(f" {message}", Colors.HEADER)
    print_colored(f"{'='*60}", Colors.HEADER)

def print_success(message):
    """Print success message"""
    print_colored(f"✓ {message}", Colors.OKGREEN)

def print_warning(message):
    """Print warning message"""
    print_colored(f"⚠ {message}", Colors.WARNING)

def print_error(message):
    """Print error message"""
    print_colored(f"✗ {message}", Colors.FAIL)

def run_command(command, cwd=None, check=True):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=check
        )
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {command}")
        print_error(f"Error: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_prerequisites():
    """Check if required software is installed"""
    print_header("Checking Prerequisites")
    
    requirements = {
        'python3': 'Python 3.8+',
        'node': 'Node.js 16+',
        'npm': 'npm package manager',
        'mongod': 'MongoDB 4.4+ (optional for local development)'
    }
    
    missing = []
    
    for cmd, desc in requirements.items():
        result = run_command(f"which {cmd}", check=False)
        if result.returncode == 0:
            print_success(f"{desc} found")
        else:
            print_warning(f"{desc} not found")
            if cmd != 'mongod':  # MongoDB is optional
                missing.append(desc)
    
    if missing:
        print_error("Missing required software:")
        for item in missing:
            print_error(f"  - {item}")
        print_error("Please install missing software and run setup again.")
        sys.exit(1)
    
    print_success("All prerequisites satisfied")

def setup_backend():
    """Setup backend environment"""
    print_header("Setting Up Backend")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print_error("Backend directory not found")
        sys.exit(1)
    
    # Create virtual environment
    print_colored("Creating Python virtual environment...")
    run_command("python3 -m venv venv", cwd=backend_dir)
    
    # Activate virtual environment and install dependencies
    print_colored("Installing Python dependencies...")
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_cmd = "venv/bin/pip"
    
    run_command(f"{pip_cmd} install --upgrade pip", cwd=backend_dir)
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir)
    
    # Setup environment file
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print_colored("Creating environment configuration...")
        shutil.copy(env_example, env_file)
        
        # Generate secret key
        secret_key = secrets.token_urlsafe(32)
        
        # Read and update .env file
        with open(env_file, 'r') as f:
            content = f.read()
        
        content = content.replace('your-secret-key-here-generate-a-secure-one', secret_key)
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print_success("Environment file created with secure secret key")
        print_warning("Please update .env file with your MongoDB URL and OpenAI API key")
    
    print_success("Backend setup completed")

def setup_frontend():
    """Setup frontend environment"""
    print_header("Setting Up Frontend")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print_error("Frontend directory not found")
        sys.exit(1)
    
    # Install dependencies
    print_colored("Installing Node.js dependencies...")
    run_command("npm install", cwd=frontend_dir)
    
    # Setup environment file
    env_file = frontend_dir / ".env"
    if not env_file.exists():
        print_colored("Creating frontend environment configuration...")
        with open(env_file, 'w') as f:
            f.write("REACT_APP_API_URL=http://localhost:8000/api\n")
            f.write("REACT_APP_ENVIRONMENT=development\n")
        print_success("Frontend environment file created")
    
    print_success("Frontend setup completed")

def setup_database():
    """Setup database"""
    print_header("Database Setup")
    
    # Check if MongoDB is running
    result = run_command("mongosh --eval 'db.runCommand({ping: 1})'", check=False)
    if result.returncode == 0:
        print_success("MongoDB is running and accessible")
    else:
        print_warning("MongoDB not accessible")
        print_colored("Please ensure MongoDB is installed and running:")
        print_colored("  - Install: https://docs.mongodb.com/manual/installation/")
        print_colored("  - Start: sudo systemctl start mongod")
        print_colored("  - Or use Docker: docker run -d -p 27017:27017 mongo:6.0")

def create_startup_scripts():
    """Create convenient startup scripts"""
    print_header("Creating Startup Scripts")
    
    # Backend startup script
    backend_script = Path("start_backend.py")
    with open(backend_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
import subprocess
import sys
from pathlib import Path

def main():
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("Backend directory not found")
        sys.exit(1)
    
    # Activate virtual environment and start server
    if sys.platform == "win32":
        python_cmd = "venv\\\\Scripts\\\\python"
    else:
        python_cmd = "venv/bin/python"
    
    print("Starting DudeAI Backend Server...")
    subprocess.run([python_cmd, "server.py"], cwd=backend_dir)

if __name__ == "__main__":
    main()
""")
    backend_script.chmod(0o755)
    
    # Frontend startup script
    frontend_script = Path("start_frontend.py")
    with open(frontend_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
import subprocess
import sys
from pathlib import Path

def main():
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("Frontend directory not found")
        sys.exit(1)
    
    print("Starting DudeAI Frontend Server...")
    subprocess.run(["npm", "start"], cwd=frontend_dir)

if __name__ == "__main__":
    main()
""")
    frontend_script.chmod(0o755)
    
    # Combined startup script
    combined_script = Path("start_dudeai.py")
    with open(combined_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
import subprocess
import sys
import time
from pathlib import Path
import threading

def start_backend():
    backend_dir = Path("backend")
    if sys.platform == "win32":
        python_cmd = "venv\\\\Scripts\\\\python"
    else:
        python_cmd = "venv/bin/python"
    
    print("Starting Backend...")
    subprocess.run([python_cmd, "server.py"], cwd=backend_dir)

def start_frontend():
    frontend_dir = Path("frontend")
    print("Starting Frontend...")
    subprocess.run(["npm", "start"], cwd=frontend_dir)

def main():
    print("Starting DudeAI AI Agent System...")
    print("Backend will start on http://localhost:8000")
    print("Frontend will start on http://localhost:3000")
    print("Press Ctrl+C to stop both servers")
    
    # Start backend in separate thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Wait a moment for backend to start
    time.sleep(3)
    
    # Start frontend (this will block)
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\\nShutting down DudeAI...")
        sys.exit(0)

if __name__ == "__main__":
    main()
""")
    combined_script.chmod(0o755)
    
    print_success("Startup scripts created:")
    print_colored("  - start_backend.py: Start backend only")
    print_colored("  - start_frontend.py: Start frontend only") 
    print_colored("  - start_dudeai.py: Start both backend and frontend")

def run_tests():
    """Run test suites"""
    print_header("Running Tests")
    
    # Backend tests
    print_colored("Running backend tests...")
    backend_dir = Path("backend")
    if os.name == 'nt':
        python_cmd = "venv\\Scripts\\python"
    else:
        python_cmd = "venv/bin/python"
    
    result = run_command(f"{python_cmd} -m pytest test_security.py -v", cwd=backend_dir, check=False)
    if result.returncode == 0:
        print_success("Backend tests passed")
    else:
        print_warning("Some backend tests failed")
    
    # Frontend tests
    print_colored("Running frontend tests...")
    frontend_dir = Path("frontend")
    result = run_command("npm test -- --watchAll=false", cwd=frontend_dir, check=False)
    if result.returncode == 0:
        print_success("Frontend tests passed")
    else:
        print_warning("Some frontend tests failed")

def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description="Setup DudeAI AI Agent System")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--skip-db", action="store_true", help="Skip database setup")
    args = parser.parse_args()
    
    print_colored("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    DudeAI AI Agent System                    ║
    ║                      Setup Script                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """, Colors.HEADER)
    
    try:
        check_prerequisites()
        setup_backend()
        setup_frontend()
        
        if not args.skip_db:
            setup_database()
        
        create_startup_scripts()
        
        if not args.skip_tests:
            run_tests()
        
        print_header("Setup Complete!")
        print_success("DudeAI AI Agent System has been set up successfully!")
        print_colored("\nNext steps:")
        print_colored("1. Update backend/.env with your MongoDB URL and OpenAI API key")
        print_colored("2. Start the system: python start_dudeai.py")
        print_colored("3. Open http://localhost:3000 in your browser")
        print_colored("\nFor production deployment, see DEPLOYMENT.md")
        
    except KeyboardInterrupt:
        print_error("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
