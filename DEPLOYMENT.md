# DudeAI AI Agent System - Deployment Guide

## Overview

This guide covers the deployment of the DudeAI AI Agent System, including backend API, frontend application, and database setup.

## Prerequisites

### System Requirements
- Python 3.8+
- Node.js 16+
- MongoDB 4.4+
- Redis 6+ (optional, for caching)
- 2GB+ RAM
- 10GB+ disk space

### Required Services
- MongoDB database
- OpenAI API key
- Domain name (for production)
- SSL certificate (for production)

## Environment Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd dudeaidemo
```

### 2. Backend Setup

#### Install Dependencies
```bash
cd backend
pip install -r requirements.txt
```

#### Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
# Database
MONGO_URL=mongodb://localhost:27017
DB_NAME=dudeai_production

# Security
SECRET_KEY=your-super-secret-key-here-generate-a-secure-one
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
```

#### Generate Secret Key
```python
import secrets
print(secrets.token_urlsafe(32))
```

### 3. Frontend Setup

#### Install Dependencies
```bash
cd frontend
npm install
# or
yarn install
```

#### Environment Configuration
Create `.env` file in frontend directory:
```env
REACT_APP_API_URL=https://api.yourdomain.com/api
REACT_APP_ENVIRONMENT=production
```

#### Build for Production
```bash
npm run build
# or
yarn build
```

## Database Setup

### 1. MongoDB Installation

#### Ubuntu/Debian
```bash
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### Docker
```bash
docker run -d \
  --name mongodb \
  -p 27017:27017 \
  -v mongodb_data:/data/db \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  mongo:6.0
```

### 2. Database Configuration

Create database and user:
```javascript
// Connect to MongoDB
use dudeai_production

// Create user (optional)
db.createUser({
  user: "dudeai_user",
  pwd: "secure_password",
  roles: [
    { role: "readWrite", db: "dudeai_production" }
  ]
})
```

## Production Deployment

### Option 1: Docker Deployment

#### 1. Create Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: dudeai_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"

  redis:
    image: redis:7-alpine
    container_name: dudeai_redis
    restart: unless-stopped
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    container_name: dudeai_backend
    restart: unless-stopped
    environment:
      - MONGO_URL=mongodb://admin:${MONGO_PASSWORD}@mongodb:27017/dudeai_production?authSource=admin
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    ports:
      - "8000:8000"

  frontend:
    build: ./frontend
    container_name: dudeai_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"

volumes:
  mongodb_data:
```

#### 2. Create Backend Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 3. Create Frontend Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 3000
CMD ["nginx", "-g", "daemon off;"]
```

#### 4. Deploy
```bash
docker-compose up -d
```

### Option 2: Traditional Server Deployment

#### 1. Backend Deployment (systemd)

Create service file:
```ini
# /etc/systemd/system/dudeai-backend.service
[Unit]
Description=DudeAI Backend API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/dudeai/backend
Environment=PATH=/var/www/dudeai/backend/venv/bin
ExecStart=/var/www/dudeai/backend/venv/bin/uvicorn server:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable dudeai-backend
sudo systemctl start dudeai-backend
```

#### 2. Frontend Deployment (Nginx)

Nginx configuration:
```nginx
# /etc/nginx/sites-available/dudeai
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Frontend
    location / {
        root /var/www/dudeai/frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/dudeai /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Using Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

## Monitoring and Logging

### 1. Application Logs
```bash
# Backend logs
sudo journalctl -u dudeai-backend -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. Health Monitoring
Set up monitoring for:
- API health endpoint: `GET /api/health`
- Database connectivity
- Disk space and memory usage
- SSL certificate expiration

### 3. Log Rotation
```bash
# /etc/logrotate.d/dudeai
/var/log/dudeai/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## Backup Strategy

### 1. Database Backup
```bash
#!/bin/bash
# backup-mongodb.sh
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="mongodb://localhost:27017/dudeai_production" --out="/backups/mongodb_$DATE"
tar -czf "/backups/mongodb_$DATE.tar.gz" "/backups/mongodb_$DATE"
rm -rf "/backups/mongodb_$DATE"

# Keep only last 7 days
find /backups -name "mongodb_*.tar.gz" -mtime +7 -delete
```

### 2. Application Backup
```bash
#!/bin/bash
# backup-app.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "/backups/app_$DATE.tar.gz" /var/www/dudeai
find /backups -name "app_*.tar.gz" -mtime +30 -delete
```

## Security Checklist

- [ ] Change default passwords
- [ ] Configure firewall (UFW/iptables)
- [ ] Enable SSL/TLS
- [ ] Set up fail2ban
- [ ] Regular security updates
- [ ] Monitor access logs
- [ ] Backup encryption
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] API rate limiting configured

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MongoDB service status
   - Verify connection string
   - Check firewall rules

2. **API 500 Errors**
   - Check application logs
   - Verify environment variables
   - Check disk space

3. **Frontend Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify environment variables

4. **SSL Certificate Issues**
   - Check certificate expiration
   - Verify domain configuration
   - Test with SSL checker tools

### Performance Optimization

1. **Database Optimization**
   - Enable MongoDB indexes
   - Configure connection pooling
   - Monitor query performance

2. **Caching**
   - Enable Redis caching
   - Configure CDN for static assets
   - Implement browser caching

3. **Application Optimization**
   - Enable gzip compression
   - Optimize bundle size
   - Use production builds

## Maintenance

### Regular Tasks
- Monitor system resources
- Update dependencies
- Review security logs
- Test backup restoration
- Performance monitoring
- SSL certificate renewal

### Updates
```bash
# Backend updates
cd /var/www/dudeai/backend
git pull
pip install -r requirements.txt
sudo systemctl restart dudeai-backend

# Frontend updates
cd /var/www/dudeai/frontend
git pull
npm install
npm run build
```
