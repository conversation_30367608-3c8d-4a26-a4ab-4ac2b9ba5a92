# DudeAI AI Agent System API Documentation

## Overview

The DudeAI AI Agent System provides a RESTful API for managing AI tools and generating comprehensive content using AI. The API includes security features, rate limiting, input validation, and comprehensive error handling.

## Base URL

```
http://localhost:8000/api
```

## Authentication

Currently, the API does not require authentication, but security headers and rate limiting are enforced.

## Rate Limiting

- General endpoints: 100 requests per minute
- Tool creation/updates: 10-20 requests per minute  
- Content generation: 5 requests per minute
- Health checks: 10 requests per minute

## Common Response Format

### Success Response
```json
{
  "data": {...},
  "message": "Success message"
}
```

### Error Response
```json
{
  "detail": "Error description",
  "status_code": 400
}
```

### Paginated Response
```json
{
  "items": [...],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 100,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```

## Endpoints

### System Endpoints

#### GET /health
Get system health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "database": {
    "status": "healthy",
    "response_time_ms": 15.2
  },
  "version": "1.0.0"
}
```

#### GET /stats
Get system statistics.

**Response:**
```json
{
  "total_tools": 150,
  "completed_tools": 120,
  "published_tools": 100,
  "verified_tools": 80,
  "pending_tools": 20,
  "processing_tools": 5,
  "failed_tools": 5,
  "average_quality_score": 85.5,
  "automation_rate": 80.0,
  "publish_rate": 66.7
}
```

#### GET /categories
Get all available categories.

**Response:**
```json
{
  "categories": [
    "Text Generation",
    "Image Generation",
    "Video Generation",
    "Audio Generation",
    "Code Generation",
    "Data Analysis",
    "Chatbots",
    "Automation",
    "Design",
    "Marketing",
    "Productivity",
    "Education",
    "Research",
    "Content Creation",
    "Translation",
    "Summarization",
    "Other"
  ]
}
```

### Tool Management Endpoints

#### GET /tools
Get paginated list of AI tools with filtering.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 20, max: 100): Items per page
- `category` (string): Filter by category
- `status` (string): Filter by status (published, draft, under_review)
- `verified` (boolean): Filter by verification status
- `search` (string): Search in name, description, company

**Example Request:**
```
GET /tools?page=1&page_size=20&category=Text%20Generation&search=chatbot
```

**Response:**
```json
{
  "items": [
    {
      "id": "uuid-here",
      "name": "ChatGPT",
      "slug": "chatgpt",
      "category": "Text Generation",
      "website": "https://chat.openai.com",
      "description": "AI chatbot by OpenAI",
      "logo_url": "https://example.com/logo.png",
      "screenshots": ["https://example.com/screenshot1.png"],
      "is_verified": true,
      "content_status": "published",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 1,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

#### POST /tools
Create a new AI tool.

**Request Body:**
```json
{
  "name": "Tool Name",
  "website": "https://example.com",
  "category": "Text Generation",
  "short_description": "Brief description",
  "company": "Company Name",
  "subcategory": "Subcategory",
  "logo_url": "https://example.com/logo.png",
  "screenshots": ["https://example.com/screenshot.png"],
  "social_links": {
    "twitter": "https://twitter.com/tool",
    "github": "https://github.com/tool"
  },
  "tags": [
    {
      "type": "Featured",
      "color": "#ff0000",
      "description": "Featured tool"
    }
  ]
}
```

**Response:** Returns the created tool object.

#### GET /tools/{tool_id}
Get a specific tool by ID.

**Path Parameters:**
- `tool_id` (string): UUID of the tool

**Response:** Returns the tool object.

#### GET /tools/slug/{slug}
Get a specific tool by slug.

**Path Parameters:**
- `slug` (string): URL-friendly slug of the tool

**Response:** Returns the tool object.

#### PUT /tools/{tool_id}
Update an existing tool.

**Path Parameters:**
- `tool_id` (string): UUID of the tool

**Request Body:** Same as POST /tools but all fields are optional.

**Response:** Returns the updated tool object.

#### DELETE /tools/{tool_id}
Delete a tool.

**Path Parameters:**
- `tool_id` (string): UUID of the tool

**Response:**
```json
{
  "message": "AI tool deleted successfully"
}
```

#### POST /tools/{tool_id}/publish
Publish a tool.

**Path Parameters:**
- `tool_id` (string): UUID of the tool

**Response:**
```json
{
  "message": "AI tool published successfully"
}
```

### AI Content Generation

#### POST /tools/{tool_id}/generate-content
Generate comprehensive AI content for a tool.

**Path Parameters:**
- `tool_id` (string): UUID of the tool

**Response:**
```json
{
  "success": true,
  "message": "Content generated successfully",
  "tool_id": "uuid-here",
  "quality_score": 85.5
}
```

## Error Codes

- `400 Bad Request`: Invalid input data or malformed request
- `401 Unauthorized`: Authentication required (future feature)
- `404 Not Found`: Resource not found
- `408 Request Timeout`: AI content generation timeout
- `413 Payload Too Large`: Request body too large
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Input Validation

### Tool Name
- Required
- 2-100 characters
- Alphanumeric and spaces allowed

### URLs
- Must be valid HTTP/HTTPS URLs
- Maximum 2048 characters
- Validated against URL pattern

### Categories
- Must be from predefined list
- Case sensitive

### Descriptions
- Maximum lengths enforced
- HTML tags stripped for security

### Screenshots
- Maximum 10 URLs per tool
- Each URL validated

## Security Features

- Input sanitization to prevent XSS
- SQL injection protection
- Rate limiting per IP
- Request size limits
- Security headers (HSTS, CSP, etc.)
- UUID validation for IDs
- URL validation for external links

## Examples

### Create a Tool
```bash
curl -X POST http://localhost:8000/api/tools \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My AI Tool",
    "website": "https://myaitool.com",
    "category": "Text Generation",
    "short_description": "An amazing AI tool"
  }'
```

### Generate Content
```bash
curl -X POST http://localhost:8000/api/tools/{tool_id}/generate-content \
  -H "Content-Type: application/json"
```

### Search Tools
```bash
curl "http://localhost:8000/api/tools?search=chatbot&category=Text%20Generation&page=1"
```
