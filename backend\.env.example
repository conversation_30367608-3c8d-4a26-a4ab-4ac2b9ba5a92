# DudeAI AI Agent System Environment Configuration

# Database Configuration
MONGO_URL=mongodb://localhost:27017
DB_NAME=dudeai_db

# Security Configuration
SECRET_KEY=your-secret-key-here-generate-a-secure-one
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# API Configuration
API_V1_STR=/api
PROJECT_NAME=DudeAI AI Agent System
VERSION=1.0.0

# CORS Configuration (comma-separated list)
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Request Limits (in bytes)
MAX_REQUEST_SIZE=10485760
MAX_CONTENT_LENGTH=1048576

# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
AI_GENERATION_TIMEOUT=300
MAX_TOKENS=3000

# Cache Configuration (optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Environment
ENVIRONMENT=development
DEBUG=true
