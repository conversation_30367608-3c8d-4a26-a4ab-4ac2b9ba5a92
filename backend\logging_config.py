"""
Comprehensive logging configuration for DudeAI AI Agent System
"""
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from config import settings

class SecurityFilter(logging.Filter):
    """Filter to remove sensitive information from logs"""
    
    SENSITIVE_PATTERNS = [
        'password', 'secret', 'key', 'token', 'auth',
        'sk-', 'mongodb://', 'redis://', 'Bearer '
    ]
    
    def filter(self, record):
        """Filter sensitive information from log records"""
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            msg = record.msg.lower()
            for pattern in self.SENSITIVE_PATTERNS:
                if pattern in msg:
                    record.msg = record.msg.replace(
                        record.msg[record.msg.lower().find(pattern):],
                        '[REDACTED]'
                    )
        return True

class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for logs"""
    
    def format(self, record):
        """Format log record as structured JSON"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'process_id': os.getpid(),
            'thread_id': record.thread,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                          'pathname', 'filename', 'module', 'lineno', 
                          'funcName', 'created', 'msecs', 'relativeCreated', 
                          'thread', 'threadName', 'processName', 'process',
                          'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return self.to_json(log_entry)
    
    def to_json(self, obj):
        """Convert object to JSON string"""
        import json
        try:
            return json.dumps(obj, default=str, ensure_ascii=False)
        except (TypeError, ValueError):
            return str(obj)

class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(f"performance.{name}")
        self.start_time = None
    
    def start(self, operation: str, **kwargs):
        """Start timing an operation"""
        self.start_time = datetime.utcnow()
        self.logger.info(
            f"Starting {operation}",
            extra={
                'operation': operation,
                'operation_start': self.start_time.isoformat(),
                **kwargs
            }
        )
    
    def end(self, operation: str, **kwargs):
        """End timing an operation"""
        if self.start_time:
            end_time = datetime.utcnow()
            duration = (end_time - self.start_time).total_seconds() * 1000  # ms
            
            self.logger.info(
                f"Completed {operation} in {duration:.2f}ms",
                extra={
                    'operation': operation,
                    'operation_end': end_time.isoformat(),
                    'duration_ms': duration,
                    **kwargs
                }
            )
            self.start_time = None

def setup_logging():
    """Setup comprehensive logging configuration"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.ENVIRONMENT == "development":
        # Human-readable format for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        # Structured format for production
        console_formatter = StructuredFormatter()
    
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(SecurityFilter())
    root_logger.addHandler(console_handler)
    
    # File handler for application logs
    app_file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "application.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    app_file_handler.setLevel(logging.DEBUG)
    app_file_handler.setFormatter(StructuredFormatter())
    app_file_handler.addFilter(SecurityFilter())
    root_logger.addHandler(app_file_handler)
    
    # Error file handler
    error_file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "errors.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(StructuredFormatter())
    error_file_handler.addFilter(SecurityFilter())
    root_logger.addHandler(error_file_handler)
    
    # Performance logger
    perf_logger = logging.getLogger("performance")
    perf_file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "performance.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    perf_file_handler.setLevel(logging.INFO)
    perf_file_handler.setFormatter(StructuredFormatter())
    perf_logger.addHandler(perf_file_handler)
    perf_logger.propagate = False
    
    # Security logger
    security_logger = logging.getLogger("security")
    security_file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "security.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    security_file_handler.setLevel(logging.INFO)
    security_file_handler.setFormatter(StructuredFormatter())
    security_logger.addHandler(security_file_handler)
    security_logger.propagate = False
    
    # Database logger
    db_logger = logging.getLogger("database")
    db_file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "database.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    db_file_handler.setLevel(logging.INFO)
    db_file_handler.setFormatter(StructuredFormatter())
    db_logger.addHandler(db_file_handler)
    db_logger.propagate = False
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("motor").setLevel(logging.WARNING)
    logging.getLogger("pymongo").setLevel(logging.WARNING)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        f"Logging configured for {settings.PROJECT_NAME}",
        extra={
            'environment': settings.ENVIRONMENT,
            'log_level': settings.LOG_LEVEL,
            'log_directory': str(log_dir.absolute())
        }
    )

def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name"""
    return logging.getLogger(name)

def get_performance_logger(name: str) -> PerformanceLogger:
    """Get a performance logger with the specified name"""
    return PerformanceLogger(name)

def log_request(request, response, duration_ms: float):
    """Log HTTP request details"""
    security_logger = logging.getLogger("security")
    
    # Extract client info
    client_ip = getattr(request.client, 'host', 'unknown') if hasattr(request, 'client') else 'unknown'
    user_agent = request.headers.get('user-agent', 'unknown')
    
    # Log request
    security_logger.info(
        f"{request.method} {request.url.path} - {response.status_code}",
        extra={
            'request_method': request.method,
            'request_path': request.url.path,
            'request_query': str(request.query_params),
            'response_status': response.status_code,
            'client_ip': client_ip,
            'user_agent': user_agent,
            'duration_ms': duration_ms,
            'request_size': request.headers.get('content-length', 0),
            'response_size': response.headers.get('content-length', 0)
        }
    )

def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "info"):
    """Log security-related events"""
    security_logger = logging.getLogger("security")
    
    log_method = getattr(security_logger, severity.lower(), security_logger.info)
    log_method(
        f"Security event: {event_type}",
        extra={
            'event_type': event_type,
            'severity': severity,
            **details
        }
    )

def log_database_operation(operation: str, collection: str, duration_ms: float, **kwargs):
    """Log database operations"""
    db_logger = logging.getLogger("database")
    
    db_logger.info(
        f"Database {operation} on {collection}",
        extra={
            'operation': operation,
            'collection': collection,
            'duration_ms': duration_ms,
            **kwargs
        }
    )

# Initialize logging when module is imported
if not logging.getLogger().handlers:
    setup_logging()
