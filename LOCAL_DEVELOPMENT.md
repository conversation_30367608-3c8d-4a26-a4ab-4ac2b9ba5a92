# DudeAI AI Agent System - Local Development Setup

This guide will help you set up the DudeAI AI Agent System for local development with proper frontend-backend communication.

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- MongoDB (local installation or Docker)
- OpenAI API key

### Automated Setup
```bash
# Run the automated setup script
python setup.py

# Start both frontend and backend
python start_dev.py
```

### Manual Setup

#### 1. Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Unix/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env

# Edit .env file with your configuration:
# - MONGO_URL=mongodb://localhost:27017
# - OPENAI_API_KEY=sk-your-api-key-here
```

#### 2. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install
```

#### 3. Start Services

**Terminal 1 - Backend:**
```bash
cd backend
python server.py
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm start
```

## Configuration Details

### Frontend Environment Variables
The frontend is configured in `frontend/.env`:

```env
# Backend API Configuration
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_BACKEND_URL=http://localhost:8000

# Environment Configuration
REACT_APP_ENVIRONMENT=development

# Development Features
REACT_APP_DEBUG=true
REACT_APP_ENABLE_LOGGING=true

# API Configuration
REACT_APP_API_TIMEOUT=30000
REACT_APP_RETRY_ATTEMPTS=3

# UI Configuration
REACT_APP_ITEMS_PER_PAGE=20
REACT_APP_SEARCH_DEBOUNCE_MS=500

# Feature Flags
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ERROR_BOUNDARY=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# Development Tools
REACT_APP_SHOW_DEV_TOOLS=true
GENERATE_SOURCEMAP=true

# Browser Configuration
BROWSER=none
PORT=3000
```

### Backend Environment Variables
The backend is configured in `backend/.env`:

```env
# Database
MONGO_URL=mongodb://localhost:27017
DB_NAME=dudeai_db

# Security
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
```

### CORS Configuration
The backend is configured to allow requests from the frontend development server:

- **Frontend URL**: `http://localhost:3000`
- **Backend URL**: `http://localhost:8000`
- **API Endpoint**: `http://localhost:8000/api`

The CORS settings in `backend/config.py` include:
```python
ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
```

## Testing the Setup

### Verify Configuration
```bash
python test_local_setup.py
```

This script will check:
- ✅ Frontend environment variables
- ✅ Backend environment variables  
- ✅ CORS configuration
- ✅ API endpoint connectivity
- ✅ Dependencies installation

### Manual Testing
1. **Backend Health Check**: Visit `http://localhost:8000/api/health`
2. **API Documentation**: Visit `http://localhost:8000/docs`
3. **Frontend Application**: Visit `http://localhost:3000`

## Development Features

### Frontend Development Features
- **Hot Reload**: Automatic page refresh on code changes
- **Error Boundary**: Graceful error handling with user-friendly messages
- **Debug Logging**: Console logging for API requests and responses
- **Performance Monitoring**: Built-in performance tracking
- **Notification System**: User-friendly success/error notifications

### Backend Development Features
- **Auto Reload**: Server restarts automatically on code changes
- **Comprehensive Logging**: Structured logging with security filtering
- **API Documentation**: Interactive Swagger/OpenAPI docs
- **Error Handling**: Detailed error responses with proper HTTP status codes
- **Security Middleware**: Input validation, rate limiting, CORS protection

## Common Issues and Solutions

### Backend Won't Start
```bash
# Check if virtual environment is activated
# Windows:
venv\Scripts\activate
# Unix/Mac:
source venv/bin/activate

# Reinstall dependencies
pip install -r requirements.txt

# Check .env file exists and has correct values
cp .env.example .env
```

### Frontend Won't Connect to Backend
1. Verify backend is running on `http://localhost:8000`
2. Check `frontend/.env` has correct `REACT_APP_API_URL`
3. Verify CORS settings in backend allow `http://localhost:3000`

### Database Connection Issues
```bash
# Start MongoDB locally
sudo systemctl start mongod

# Or use Docker
docker run -d -p 27017:27017 mongo:6.0

# Update MONGO_URL in backend/.env
MONGO_URL=mongodb://localhost:27017
```

### Port Already in Use
```bash
# Find process using port 3000 or 8000
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# Kill process (Windows)
taskkill /PID <process_id> /F

# Kill process (Unix/Mac)
kill -9 <process_id>
```

## Development Workflow

1. **Start Development Servers**:
   ```bash
   python start_dev.py
   ```

2. **Make Changes**:
   - Frontend changes auto-reload at `http://localhost:3000`
   - Backend changes require server restart

3. **Test Changes**:
   ```bash
   # Run backend tests
   cd backend && python -m pytest

   # Run frontend tests
   cd frontend && npm test
   ```

4. **Debug Issues**:
   - Check browser console for frontend errors
   - Check terminal output for backend errors
   - Use API docs at `http://localhost:8000/docs` for API testing

## URLs Reference

| Service | URL | Description |
|---------|-----|-------------|
| Frontend | http://localhost:3000 | React development server |
| Backend API | http://localhost:8000/api | FastAPI backend |
| API Docs | http://localhost:8000/docs | Interactive API documentation |
| Health Check | http://localhost:8000/api/health | Backend health status |

## Next Steps

Once you have the local development environment running:

1. **Explore the API**: Visit `http://localhost:8000/docs`
2. **Test the Frontend**: Visit `http://localhost:3000`
3. **Run Tests**: Use `python run_tests.py`
4. **Read Documentation**: Check `backend/docs/API.md`
5. **Deploy**: Follow `DEPLOYMENT.md` for production setup
