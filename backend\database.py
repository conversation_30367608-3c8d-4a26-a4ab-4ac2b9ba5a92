"""
Database utilities and connection management for DudeAI AI Agent System
"""
import asyncio
import logging
from typing import Optional, Dict, Any, List
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from pymongo import IndexModel, TEXT
import time

from config import settings

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection and operations manager"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self._connection_retries = 0
        self._max_retries = 3
        self._retry_delay = 5  # seconds
    
    async def connect(self) -> bool:
        """Connect to MongoDB with retry logic"""
        for attempt in range(self._max_retries):
            try:
                logger.info(f"Attempting to connect to MongoDB (attempt {attempt + 1}/{self._max_retries})")
                
                # Create client with connection pooling settings
                self.client = AsyncIOMotorClient(
                    settings.MONGO_URL,
                    maxPoolSize=50,
                    minPoolSize=10,
                    maxIdleTimeMS=30000,
                    serverSelectionTimeoutMS=5000,
                    connectTimeoutMS=10000,
                    socketTimeoutMS=20000,
                )
                
                # Test the connection
                await self.client.admin.command('ping')
                
                self.database = self.client[settings.DB_NAME]
                logger.info("Successfully connected to MongoDB")
                
                # Create indexes
                await self._create_indexes()
                
                return True
                
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                logger.error(f"Failed to connect to MongoDB: {str(e)}")
                if attempt < self._max_retries - 1:
                    logger.info(f"Retrying in {self._retry_delay} seconds...")
                    await asyncio.sleep(self._retry_delay)
                else:
                    logger.error("Max connection retries exceeded")
                    return False
            except Exception as e:
                logger.error(f"Unexpected error connecting to MongoDB: {str(e)}")
                return False
        
        return False
    
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    async def _create_indexes(self):
        """Create database indexes for performance"""
        try:
            collection = self.database.ai_tools
            
            # Define indexes
            indexes = [
                IndexModel("id", unique=True),
                IndexModel("slug", unique=True),
                IndexModel("category"),
                IndexModel("ai_generation_status"),
                IndexModel("content_status"),
                IndexModel("is_verified"),
                IndexModel("created_at"),
                IndexModel("updated_at"),
                IndexModel([("name", TEXT), ("description", TEXT), ("short_description", TEXT)]),
                IndexModel([("category", 1), ("content_status", 1)]),
                IndexModel([("ai_generation_status", 1), ("created_at", -1)]),
            ]
            
            # Create indexes
            await collection.create_indexes(indexes)
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")
            # Don't raise exception - indexes are for performance, not functionality
    
    async def health_check(self) -> Dict[str, Any]:
        """Check database health"""
        try:
            if not self.client or not self.database:
                return {"status": "disconnected", "error": "No database connection"}
            
            # Test connection
            start_time = time.time()
            await self.client.admin.command('ping')
            response_time = (time.time() - start_time) * 1000  # ms
            
            # Get database stats
            stats = await self.database.command("dbStats")
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "database": settings.DB_NAME,
                "collections": stats.get("collections", 0),
                "data_size": stats.get("dataSize", 0),
                "index_size": stats.get("indexSize", 0)
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {"status": "unhealthy", "error": str(e)}
    
    def get_collection(self, name: str):
        """Get a collection from the database"""
        if not self.database:
            raise RuntimeError("Database not connected")
        return self.database[name]

class DatabaseOperations:
    """Database operations with error handling"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def get_tools_with_pagination(
        self, 
        page: int = 1, 
        page_size: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_at",
        sort_order: int = -1
    ) -> Dict[str, Any]:
        """Get tools with pagination and filtering"""
        try:
            collection = self.db_manager.get_collection("ai_tools")
            
            # Build query
            query = filters or {}
            
            # Calculate skip
            skip = (page - 1) * page_size
            
            # Get total count
            total_count = await collection.count_documents(query)
            
            # Get tools
            cursor = collection.find(query).sort(sort_by, sort_order).skip(skip).limit(page_size)
            tools = await cursor.to_list(page_size)
            
            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            
            return {
                "tools": tools,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting tools with pagination: {str(e)}")
            raise
    
    async def get_statistics_optimized(self) -> Dict[str, Any]:
        """Get statistics using optimized aggregation pipeline"""
        try:
            collection = self.db_manager.get_collection("ai_tools")
            
            # Single aggregation pipeline for all statistics
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_tools": {"$sum": 1},
                        "completed_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$ai_generation_status", "completed"]}, 1, 0]
                            }
                        },
                        "published_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$content_status", "published"]}, 1, 0]
                            }
                        },
                        "pending_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$ai_generation_status", "pending"]}, 1, 0]
                            }
                        },
                        "processing_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$ai_generation_status", "processing"]}, 1, 0]
                            }
                        },
                        "failed_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$ai_generation_status", "failed"]}, 1, 0]
                            }
                        },
                        "verified_tools": {
                            "$sum": {
                                "$cond": [{"$eq": ["$is_verified", True]}, 1, 0]
                            }
                        },
                        "avg_quality_score": {
                            "$avg": {
                                "$cond": [
                                    {"$ne": ["$content_quality_score", None]},
                                    "$content_quality_score",
                                    None
                                ]
                            }
                        }
                    }
                }
            ]
            
            result = await collection.aggregate(pipeline).to_list(1)
            
            if not result:
                return {
                    "total_tools": 0,
                    "completed_tools": 0,
                    "published_tools": 0,
                    "verified_tools": 0,
                    "pending_tools": 0,
                    "processing_tools": 0,
                    "failed_tools": 0,
                    "average_quality_score": 0,
                    "automation_rate": 0,
                    "publish_rate": 0
                }
            
            stats = result[0]
            total_tools = stats["total_tools"]
            completed_tools = stats["completed_tools"]
            published_tools = stats["published_tools"]
            
            # Calculate rates
            automation_rate = round((completed_tools / total_tools * 100), 2) if total_tools > 0 else 0
            publish_rate = round((published_tools / total_tools * 100), 2) if total_tools > 0 else 0
            avg_quality = round(stats["avg_quality_score"], 2) if stats["avg_quality_score"] else 0
            
            return {
                "total_tools": total_tools,
                "completed_tools": completed_tools,
                "published_tools": published_tools,
                "verified_tools": stats["verified_tools"],
                "pending_tools": stats["pending_tools"],
                "processing_tools": stats["processing_tools"],
                "failed_tools": stats["failed_tools"],
                "average_quality_score": avg_quality,
                "automation_rate": automation_rate,
                "publish_rate": publish_rate
            }
            
        except Exception as e:
            logger.error(f"Error getting optimized statistics: {str(e)}")
            raise

# Global database manager instance
db_manager = DatabaseManager()
db_operations = DatabaseOperations(db_manager)
