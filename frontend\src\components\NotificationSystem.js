import React, { useState, useEffect, useCallback } from 'react';
import { parseError, formatErrorMessage, shouldShowRetry, getErrorColor } from '../utils/errorHandling';

// Notification types
const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Individual notification component
const Notification = ({ notification, onClose, onRetry }) => {
  const { id, type, message, error, retryAction, autoClose = true, duration = 5000 } = notification;

  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        onClose(id);
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [id, autoClose, duration, onClose]);

  const getNotificationStyles = () => {
    const baseStyles = "fixed top-4 right-4 max-w-md w-full bg-white border-l-4 rounded-lg shadow-lg z-50 p-4";
    
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return `${baseStyles} border-green-500`;
      case NOTIFICATION_TYPES.ERROR:
        return `${baseStyles} border-red-500`;
      case NOTIFICATION_TYPES.WARNING:
        return `${baseStyles} border-yellow-500`;
      case NOTIFICATION_TYPES.INFO:
        return `${baseStyles} border-blue-500`;
      default:
        return `${baseStyles} border-gray-500`;
    }
  };

  const getIconStyles = () => {
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return "text-green-500";
      case NOTIFICATION_TYPES.ERROR:
        return "text-red-500";
      case NOTIFICATION_TYPES.WARNING:
        return "text-yellow-500";
      case NOTIFICATION_TYPES.INFO:
        return "text-blue-500";
      default:
        return "text-gray-500";
    }
  };

  const getIcon = () => {
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return "✓";
      case NOTIFICATION_TYPES.ERROR:
        return "✕";
      case NOTIFICATION_TYPES.WARNING:
        return "⚠";
      case NOTIFICATION_TYPES.INFO:
        return "ℹ";
      default:
        return "•";
    }
  };

  const showRetryButton = error && shouldShowRetry(error) && retryAction;

  return (
    <div className={getNotificationStyles()} style={{ top: `${4 + (notification.index || 0) * 80}px` }}>
      <div className="flex items-start">
        <div className={`flex-shrink-0 ${getIconStyles()}`}>
          <span className="text-xl font-bold">{getIcon()}</span>
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-900">
            {message || (error ? formatErrorMessage(error) : 'Notification')}
          </p>
          {error && process.env.NODE_ENV === 'development' && (
            <details className="mt-2">
              <summary className="text-xs text-gray-500 cursor-pointer">
                Error Details (Development)
              </summary>
              <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(parseError(error), null, 2)}
              </pre>
            </details>
          )}
          {showRetryButton && (
            <button
              onClick={() => onRetry(retryAction)}
              className="mt-2 text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Retry
            </button>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={() => onClose(id)}
            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            <span className="sr-only">Close</span>
            <span className="text-lg">×</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// Main notification system component
const NotificationSystem = () => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      ...notification,
      index: notifications.length
    };

    setNotifications(prev => [...prev, newNotification]);
    return id;
  }, [notifications.length]);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const handleRetry = useCallback((retryAction) => {
    if (typeof retryAction === 'function') {
      retryAction();
    }
  }, []);

  // Update indices when notifications change
  useEffect(() => {
    setNotifications(prev => 
      prev.map((notification, index) => ({
        ...notification,
        index
      }))
    );
  }, [notifications.length]);

  return (
    <>
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          notification={notification}
          onClose={removeNotification}
          onRetry={handleRetry}
        />
      ))}
    </>
  );
};

// Hook for using notifications
export const useNotifications = () => {
  const [notificationSystem, setNotificationSystem] = useState(null);

  useEffect(() => {
    // Find or create notification system instance
    let system = window.__notificationSystem;
    if (!system) {
      system = {
        notifications: [],
        listeners: [],
        addNotification: (notification) => {
          const id = Date.now() + Math.random();
          const newNotification = { id, ...notification };
          system.notifications.push(newNotification);
          system.listeners.forEach(listener => listener([...system.notifications]));
          return id;
        },
        removeNotification: (id) => {
          system.notifications = system.notifications.filter(n => n.id !== id);
          system.listeners.forEach(listener => listener([...system.notifications]));
        },
        clearAll: () => {
          system.notifications = [];
          system.listeners.forEach(listener => listener([]));
        },
        subscribe: (listener) => {
          system.listeners.push(listener);
          return () => {
            system.listeners = system.listeners.filter(l => l !== listener);
          };
        }
      };
      window.__notificationSystem = system;
    }
    setNotificationSystem(system);
  }, []);

  const showSuccess = useCallback((message, options = {}) => {
    if (notificationSystem) {
      return notificationSystem.addNotification({
        type: NOTIFICATION_TYPES.SUCCESS,
        message,
        ...options
      });
    }
  }, [notificationSystem]);

  const showError = useCallback((error, options = {}) => {
    if (notificationSystem) {
      return notificationSystem.addNotification({
        type: NOTIFICATION_TYPES.ERROR,
        error,
        ...options
      });
    }
  }, [notificationSystem]);

  const showWarning = useCallback((message, options = {}) => {
    if (notificationSystem) {
      return notificationSystem.addNotification({
        type: NOTIFICATION_TYPES.WARNING,
        message,
        ...options
      });
    }
  }, [notificationSystem]);

  const showInfo = useCallback((message, options = {}) => {
    if (notificationSystem) {
      return notificationSystem.addNotification({
        type: NOTIFICATION_TYPES.INFO,
        message,
        ...options
      });
    }
  }, [notificationSystem]);

  const clearAll = useCallback(() => {
    if (notificationSystem) {
      notificationSystem.clearAll();
    }
  }, [notificationSystem]);

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearAll
  };
};

// Global notification system component that should be rendered once at app root
export const GlobalNotificationSystem = () => {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    const system = window.__notificationSystem;
    if (system) {
      const unsubscribe = system.subscribe(setNotifications);
      setNotifications([...system.notifications]);
      return unsubscribe;
    }
  }, []);

  const removeNotification = useCallback((id) => {
    const system = window.__notificationSystem;
    if (system) {
      system.removeNotification(id);
    }
  }, []);

  const handleRetry = useCallback((retryAction) => {
    if (typeof retryAction === 'function') {
      retryAction();
    }
  }, []);

  return (
    <>
      {notifications.map((notification, index) => (
        <Notification
          key={notification.id}
          notification={{ ...notification, index }}
          onClose={removeNotification}
          onRetry={handleRetry}
        />
      ))}
    </>
  );
};

export default NotificationSystem;
export { NOTIFICATION_TYPES };
