#!/usr/bin/env python3
import asyncio
import httpx
import json
import time
import os
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Get backend URL from environment or use default
BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
API_URL = f"{BACKEND_URL}/api"

# Test configuration
TEST_TIMEOUT = 30
MAX_RETRIES = 3

# Test data
TEST_TOOL = {
    "name": "Claude AI",
    "category": "Conversational AI",
    "subcategory": "AI Assistant",
    "company": "Anthropic",
    "website": "https://claude.ai",
    "short_description": "Advanced AI assistant for natural conversations and content creation",
    "logo_url": "https://storage.googleapis.com/anthropic-website/images/claude-logo.svg",
    "screenshots": [
        "https://storage.googleapis.com/anthropic-website/images/claude-screenshot-1.png",
        "https://storage.googleapis.com/anthropic-website/images/claude-screenshot-2.png"
    ],
    "social_links": {
        "twitter": "https://twitter.com/AnthropicAI",
        "linkedin": "https://www.linkedin.com/company/anthropic/",
        "github": "https://github.com/anthropics",
        "facebook": "https://www.facebook.com/anthropicai",
        "youtube": "https://www.youtube.com/channel/anthropic"
    },
    "tags": [
        {
            "type": "Trending",
            "color": "#FF5733"
        },
        {
            "type": "Premium",
            "color": "#33A1FF"
        }
    ]
}

class BackendTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=60.0)  # Longer timeout for content generation
        self.created_tool_id = None
        self.created_tool_slug = None
        self.test_results = {
            "AI Tool CRUD Operations": {"success": False, "details": ""},
            "OpenAI GPT-4o Integration": {"success": False, "details": ""},
            "AI Content Generation Engine": {"success": False, "details": ""},
            "Quality Assessment System": {"success": False, "details": ""},
            "Statistics and Analytics API": {"success": False, "details": ""},
            "Enhanced Tool Creation": {"success": False, "details": ""},
            "Enhanced Content Generation": {"success": False, "details": ""},
            "Slug-based Tool Retrieval": {"success": False, "details": ""},
            "Tool Update Endpoint": {"success": False, "details": ""},
            "Tool Publishing Workflow": {"success": False, "details": ""},
            "Enhanced Filtering": {"success": False, "details": ""}
        }
    
    async def close(self):
        await self.client.aclose()
    
    async def test_create_ai_tool(self) -> Dict[str, Any]:
        """Test creating a new AI tool with comprehensive fields"""
        try:
            logger.info(f"Creating AI tool: {TEST_TOOL['name']}")
            response = await self.client.post(f"{API_URL}/tools", json=TEST_TOOL)
            
            if response.status_code != 200:
                error_msg = f"Failed to create AI tool. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["AI Tool CRUD Operations"]["details"] += f"Create tool failed: {error_msg}\n"
                self.test_results["Enhanced Tool Creation"]["details"] += f"Create tool failed: {error_msg}\n"
                return None
            
            tool_data = response.json()
            self.created_tool_id = tool_data["id"]
            self.created_tool_slug = tool_data["slug"]
            logger.info(f"Successfully created AI tool with ID: {self.created_tool_id}")
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Create tool succeeded: {tool_data['name']} (ID: {self.created_tool_id})\n"
            
            # Verify comprehensive fields
            self._verify_enhanced_tool_creation(tool_data)
            
            return tool_data
        except Exception as e:
            error_msg = f"Exception creating AI tool: {str(e)}"
            logger.error(error_msg)
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Create tool exception: {error_msg}\n"
            self.test_results["Enhanced Tool Creation"]["details"] += f"Create tool exception: {error_msg}\n"
            return None
    
    def _verify_enhanced_tool_creation(self, tool_data):
        """Verify all enhanced fields in the created tool"""
        # Check basic info fields
        basic_info_fields = ["name", "category", "subcategory", "company", "website", "short_description"]
        missing_basic = [field for field in basic_info_fields if field in TEST_TOOL and (field not in tool_data or tool_data[field] != TEST_TOOL[field])]
        
        if missing_basic:
            self.test_results["Enhanced Tool Creation"]["details"] += f"Missing or incorrect basic info fields: {', '.join(missing_basic)}\n"
        else:
            self.test_results["Enhanced Tool Creation"]["details"] += "All basic info fields correctly stored\n"
        
        # Check visual assets
        if "logo_url" in TEST_TOOL and (tool_data.get("logo_url") != TEST_TOOL["logo_url"]):
            self.test_results["Enhanced Tool Creation"]["details"] += "Logo URL not correctly stored\n"
        else:
            self.test_results["Enhanced Tool Creation"]["details"] += "Logo URL correctly stored\n"
        
        if "screenshots" in TEST_TOOL:
            if not tool_data.get("screenshots") or len(tool_data["screenshots"]) != len(TEST_TOOL["screenshots"]):
                self.test_results["Enhanced Tool Creation"]["details"] += "Screenshots array not correctly stored\n"
            else:
                self.test_results["Enhanced Tool Creation"]["details"] += f"Screenshots array correctly stored ({len(tool_data['screenshots'])} items)\n"
        
        # Check social links
        if "social_links" in TEST_TOOL:
            if not tool_data.get("social_links"):
                self.test_results["Enhanced Tool Creation"]["details"] += "Social links object missing\n"
            else:
                social_links = tool_data["social_links"]
                expected_links = TEST_TOOL["social_links"]
                missing_links = [link for link in expected_links if link not in social_links or social_links[link] != expected_links[link]]
                
                if missing_links:
                    self.test_results["Enhanced Tool Creation"]["details"] += f"Missing or incorrect social links: {', '.join(missing_links)}\n"
                else:
                    self.test_results["Enhanced Tool Creation"]["details"] += "All social links correctly stored\n"
        
        # Check tags
        if "tags" in TEST_TOOL:
            if not tool_data.get("tags") or len(tool_data["tags"]) != len(TEST_TOOL["tags"]):
                self.test_results["Enhanced Tool Creation"]["details"] += "Tags array not correctly stored\n"
            else:
                # Check if all tag types and colors are preserved
                all_types_match = all(
                    any(
                        created_tag["type"] == expected_tag["type"] and 
                        created_tag.get("color") == expected_tag.get("color")
                        for created_tag in tool_data["tags"]
                    )
                    for expected_tag in TEST_TOOL["tags"]
                )
                
                if all_types_match:
                    self.test_results["Enhanced Tool Creation"]["details"] += f"Tags array correctly stored with types and colors\n"
                else:
                    self.test_results["Enhanced Tool Creation"]["details"] += "Tags array missing type or color information\n"
        
        # Check auto-generated fields
        if not tool_data.get("slug"):
            self.test_results["Enhanced Tool Creation"]["details"] += "Slug not auto-generated\n"
        else:
            self.test_results["Enhanced Tool Creation"]["details"] += f"Slug auto-generated: {tool_data['slug']}\n"
        
        if not tool_data.get("link"):
            self.test_results["Enhanced Tool Creation"]["details"] += "Link not auto-generated\n"
        else:
            self.test_results["Enhanced Tool Creation"]["details"] += f"Link auto-generated: {tool_data['link']}\n"
    
    async def test_get_ai_tools(self) -> List[Dict[str, Any]]:
        """Test getting all AI tools"""
        try:
            logger.info("Getting all AI tools")
            response = await self.client.get(f"{API_URL}/tools")
            
            if response.status_code != 200:
                error_msg = f"Failed to get AI tools. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["AI Tool CRUD Operations"]["details"] += f"Get all tools failed: {error_msg}\n"
                return []
            
            tools = response.json()
            logger.info(f"Successfully retrieved {len(tools)} AI tools")
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Get all tools succeeded: {len(tools)} tools retrieved\n"
            
            # Check if our created tool is in the list
            found = any(tool["id"] == self.created_tool_id for tool in tools)
            if found:
                logger.info(f"Found our created tool in the list")
                self.test_results["AI Tool CRUD Operations"]["details"] += f"Created tool found in list\n"
            else:
                logger.warning(f"Our created tool was not found in the list")
                self.test_results["AI Tool CRUD Operations"]["details"] += f"Warning: Created tool not found in list\n"
            
            return tools
        except Exception as e:
            error_msg = f"Exception getting AI tools: {str(e)}"
            logger.error(error_msg)
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Get all tools exception: {error_msg}\n"
            return []
    
    async def test_get_ai_tool_by_id(self) -> Optional[Dict[str, Any]]:
        """Test getting a specific AI tool by ID"""
        if not self.created_tool_id:
            logger.error("Cannot get tool by ID: No tool ID available")
            self.test_results["AI Tool CRUD Operations"]["details"] += "Get tool by ID skipped: No tool ID available\n"
            return None
        
        try:
            logger.info(f"Getting AI tool by ID: {self.created_tool_id}")
            response = await self.client.get(f"{API_URL}/tools/{self.created_tool_id}")
            
            if response.status_code != 200:
                error_msg = f"Failed to get AI tool by ID. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["AI Tool CRUD Operations"]["details"] += f"Get tool by ID failed: {error_msg}\n"
                return None
            
            tool = response.json()
            logger.info(f"Successfully retrieved AI tool: {tool['name']}")
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Get tool by ID succeeded: {tool['name']}\n"
            return tool
        except Exception as e:
            error_msg = f"Exception getting AI tool by ID: {str(e)}"
            logger.error(error_msg)
            self.test_results["AI Tool CRUD Operations"]["details"] += f"Get tool by ID exception: {error_msg}\n"
            return None
    
    async def test_generate_content(self) -> Optional[Dict[str, Any]]:
        """Test generating content for an AI tool"""
        if not self.created_tool_id:
            logger.error("Cannot generate content: No tool ID available")
            self.test_results["AI Content Generation Engine"]["details"] += "Content generation skipped: No tool ID available\n"
            return None
        
        try:
            logger.info(f"Generating content for AI tool: {self.created_tool_id}")
            response = await self.client.post(f"{API_URL}/tools/{self.created_tool_id}/generate-content")
            
            if response.status_code != 200:
                error_msg = f"Failed to generate content. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["AI Content Generation Engine"]["details"] += f"Content generation failed: {error_msg}\n"
                self.test_results["OpenAI GPT-4o Integration"]["details"] += f"OpenAI integration failed: {error_msg}\n"
                return None
            
            result = response.json()
            logger.info(f"Content generation initiated: {result['message']}")
            self.test_results["AI Content Generation Engine"]["details"] += f"Content generation initiated: {result['message']}\n"
            
            # Check if quality score is included
            if result.get('quality_score') is not None:
                logger.info(f"Quality score: {result['quality_score']}")
                self.test_results["Quality Assessment System"]["details"] += f"Quality score received: {result['quality_score']}\n"
            
            return result
        except Exception as e:
            error_msg = f"Exception generating content: {str(e)}"
            logger.error(error_msg)
            self.test_results["AI Content Generation Engine"]["details"] += f"Content generation exception: {error_msg}\n"
            self.test_results["OpenAI GPT-4o Integration"]["details"] += f"OpenAI integration exception: {error_msg}\n"
            return None
    
    async def wait_for_content_generation(self, max_attempts=10, delay=3) -> Optional[Dict[str, Any]]:
        """Wait for content generation to complete"""
        if not self.created_tool_id:
            logger.error("Cannot wait for content generation: No tool ID available")
            return None
        
        logger.info(f"Waiting for content generation to complete for tool: {self.created_tool_id}")
        
        for attempt in range(max_attempts):
            try:
                response = await self.client.get(f"{API_URL}/tools/{self.created_tool_id}")
                
                if response.status_code != 200:
                    logger.warning(f"Failed to get tool status. Status: {response.status_code}")
                    await asyncio.sleep(delay)
                    continue
                
                tool = response.json()
                status = tool.get('generation_status', 'unknown')
                
                logger.info(f"Generation status (attempt {attempt+1}/{max_attempts}): {status}")
                
                if status == 'completed':
                    logger.info("Content generation completed successfully")
                    self.test_results["AI Content Generation Engine"]["details"] += "Content generation completed successfully\n"
                    self.test_results["OpenAI GPT-4o Integration"]["details"] += "OpenAI integration successful\n"
                    
                    # Check generated content
                    content_fields = [
                        ('ai_description', 'description'),
                        ('ai_features', 'features'),
                        ('ai_pricing', 'pricing'),
                        ('ai_pros', 'pros'),
                        ('ai_cons', 'cons'),
                        ('ai_use_cases', 'use cases'),
                        ('ai_comparison', 'comparison')
                    ]
                    
                    content_details = []
                    for field, label in content_fields:
                        if tool.get(field):
                            if isinstance(tool[field], list):
                                content_details.append(f"{label}: {len(tool[field])} items")
                            else:
                                content_details.append(f"{label}: {len(str(tool[field]))} chars")
                    
                    self.test_results["AI Content Generation Engine"]["details"] += f"Generated content: {', '.join(content_details)}\n"
                    
                    # Check quality score
                    if tool.get('quality_score') is not None:
                        score = tool['quality_score']
                        logger.info(f"Quality score: {score}")
                        self.test_results["Quality Assessment System"]["details"] += f"Final quality score: {score}\n"
                        
                        # Validate score is in range 0-100
                        if 0 <= score <= 100:
                            self.test_results["Quality Assessment System"]["details"] += "Quality score is in valid range (0-100)\n"
                        else:
                            self.test_results["Quality Assessment System"]["details"] += f"Warning: Quality score {score} is outside valid range (0-100)\n"
                    
                    return tool
                
                elif status == 'failed':
                    logger.error("Content generation failed")
                    self.test_results["AI Content Generation Engine"]["details"] += "Content generation failed\n"
                    self.test_results["OpenAI GPT-4o Integration"]["details"] += "OpenAI integration failed\n"
                    return None
                
                # Continue waiting if still processing or pending
                await asyncio.sleep(delay)
                
            except Exception as e:
                logger.error(f"Error checking generation status: {str(e)}")
                await asyncio.sleep(delay)
        
        logger.warning(f"Timed out waiting for content generation after {max_attempts} attempts")
        self.test_results["AI Content Generation Engine"]["details"] += f"Warning: Timed out waiting for content generation\n"
        return None
    
    async def test_stats_api(self) -> Optional[Dict[str, Any]]:
        """Test the statistics API"""
        try:
            logger.info("Getting system statistics")
            response = await self.client.get(f"{API_URL}/stats")
            
            if response.status_code != 200:
                error_msg = f"Failed to get statistics. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["Statistics and Analytics API"]["details"] += f"Statistics API failed: {error_msg}\n"
                return None
            
            stats = response.json()
            logger.info(f"Successfully retrieved system statistics")
            
            # Check if all expected fields are present
            expected_fields = [
                'total_tools', 'completed_tools', 'pending_tools', 
                'processing_tools', 'failed_tools', 'average_quality_score',
                'automation_rate'
            ]
            
            missing_fields = [field for field in expected_fields if field not in stats]
            
            if missing_fields:
                logger.warning(f"Statistics missing fields: {', '.join(missing_fields)}")
                self.test_results["Statistics and Analytics API"]["details"] += f"Warning: Statistics missing fields: {', '.join(missing_fields)}\n"
            else:
                logger.info("Statistics include all expected fields")
                self.test_results["Statistics and Analytics API"]["details"] += "Statistics include all expected fields\n"
            
            # Log the actual statistics
            stats_details = []
            for key, value in stats.items():
                stats_details.append(f"{key}: {value}")
            
            self.test_results["Statistics and Analytics API"]["details"] += f"Statistics values: {', '.join(stats_details)}\n"
            
            return stats
        except Exception as e:
            error_msg = f"Exception getting statistics: {str(e)}"
            logger.error(error_msg)
            self.test_results["Statistics and Analytics API"]["details"] += f"Statistics API exception: {error_msg}\n"
            return None
    
    async def test_get_ai_tool_by_slug(self) -> Optional[Dict[str, Any]]:
        """Test getting a specific AI tool by slug"""
        if not self.created_tool_slug:
            logger.error("Cannot get tool by slug: No tool slug available")
            self.test_results["Slug-based Tool Retrieval"]["details"] += "Get tool by slug skipped: No tool slug available\n"
            return None
        
        try:
            logger.info(f"Getting AI tool by slug: {self.created_tool_slug}")
            response = await self.client.get(f"{API_URL}/tools/slug/{self.created_tool_slug}")
            
            if response.status_code != 200:
                error_msg = f"Failed to get AI tool by slug. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["Slug-based Tool Retrieval"]["details"] += f"Get tool by slug failed: {error_msg}\n"
                return None
            
            tool = response.json()
            logger.info(f"Successfully retrieved AI tool by slug: {tool['name']}")
            self.test_results["Slug-based Tool Retrieval"]["details"] += f"Get tool by slug succeeded: {tool['name']}\n"
            
            # Verify the retrieved tool matches our created tool
            if tool["id"] == self.created_tool_id:
                self.test_results["Slug-based Tool Retrieval"]["details"] += "Retrieved tool ID matches created tool ID\n"
            else:
                self.test_results["Slug-based Tool Retrieval"]["details"] += "Warning: Retrieved tool ID does not match created tool ID\n"
            
            return tool
        except Exception as e:
            error_msg = f"Exception getting AI tool by slug: {str(e)}"
            logger.error(error_msg)
            self.test_results["Slug-based Tool Retrieval"]["details"] += f"Get tool by slug exception: {error_msg}\n"
            return None
    
    async def test_update_ai_tool(self) -> Optional[Dict[str, Any]]:
        """Test updating an AI tool with new fields"""
        if not self.created_tool_id:
            logger.error("Cannot update tool: No tool ID available")
            self.test_results["Tool Update Endpoint"]["details"] += "Update tool skipped: No tool ID available\n"
            return None
        
        try:
            # Create update data with new values
            update_data = {
                "name": f"{TEST_TOOL['name']} Updated",
                "short_description": "Updated description for testing",
                "meta_title": "Claude AI - Best AI Assistant for Natural Conversations",
                "meta_description": "Claude AI by Anthropic is an advanced AI assistant that excels at natural conversations and content creation.",
                "meta_keywords": "AI assistant, Claude, Anthropic, natural language, content creation",
                "content_status": "under_review",
                "is_verified": True,
                "hashtags": ["#ClaudeAI", "#AnthropicAI", "#AIAssistant"]
            }
            
            logger.info(f"Updating AI tool: {self.created_tool_id}")
            response = await self.client.put(f"{API_URL}/tools/{self.created_tool_id}", json=update_data)
            
            if response.status_code != 200:
                error_msg = f"Failed to update AI tool. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["Tool Update Endpoint"]["details"] += f"Update tool failed: {error_msg}\n"
                return None
            
            updated_tool = response.json()
            logger.info(f"Successfully updated AI tool: {updated_tool['name']}")
            self.test_results["Tool Update Endpoint"]["details"] += f"Update tool succeeded: {updated_tool['name']}\n"
            
            # Verify updated fields
            for field, value in update_data.items():
                if updated_tool.get(field) != value:
                    self.test_results["Tool Update Endpoint"]["details"] += f"Field '{field}' not updated correctly\n"
                else:
                    self.test_results["Tool Update Endpoint"]["details"] += f"Field '{field}' updated correctly\n"
            
            # Check if version was incremented
            if updated_tool.get("version", 1) > 1:
                self.test_results["Tool Update Endpoint"]["details"] += f"Version incremented to {updated_tool['version']}\n"
            else:
                self.test_results["Tool Update Endpoint"]["details"] += "Warning: Version not incremented\n"
            
            return updated_tool
        except Exception as e:
            error_msg = f"Exception updating AI tool: {str(e)}"
            logger.error(error_msg)
            self.test_results["Tool Update Endpoint"]["details"] += f"Update tool exception: {error_msg}\n"
            return None
    
    async def test_publish_ai_tool(self) -> bool:
        """Test publishing an AI tool"""
        if not self.created_tool_id:
            logger.error("Cannot publish tool: No tool ID available")
            self.test_results["Tool Publishing Workflow"]["details"] += "Publish tool skipped: No tool ID available\n"
            return False
        
        try:
            logger.info(f"Publishing AI tool: {self.created_tool_id}")
            response = await self.client.post(f"{API_URL}/tools/{self.created_tool_id}/publish")
            
            if response.status_code != 200:
                error_msg = f"Failed to publish AI tool. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                self.test_results["Tool Publishing Workflow"]["details"] += f"Publish tool failed: {error_msg}\n"
                return False
            
            result = response.json()
            logger.info(f"Successfully published AI tool: {result.get('message', '')}")
            self.test_results["Tool Publishing Workflow"]["details"] += f"Publish tool succeeded: {result.get('message', '')}\n"
            
            # Verify the tool was actually published by retrieving it
            tool = await self.test_get_ai_tool_by_id()
            if tool and tool.get("content_status") == "published":
                self.test_results["Tool Publishing Workflow"]["details"] += "Tool status correctly set to 'published'\n"
                
                # Check if published_at timestamp was set
                if tool.get("published_at"):
                    self.test_results["Tool Publishing Workflow"]["details"] += "published_at timestamp correctly set\n"
                else:
                    self.test_results["Tool Publishing Workflow"]["details"] += "Warning: published_at timestamp not set\n"
                
                return True
            else:
                self.test_results["Tool Publishing Workflow"]["details"] += "Warning: Tool status not set to 'published' after publish request\n"
                return False
            
        except Exception as e:
            error_msg = f"Exception publishing AI tool: {str(e)}"
            logger.error(error_msg)
            self.test_results["Tool Publishing Workflow"]["details"] += f"Publish tool exception: {error_msg}\n"
            return False
    
    async def test_enhanced_filtering(self) -> bool:
        """Test enhanced filtering capabilities"""
        try:
            logger.info("Testing enhanced filtering capabilities")
            self.test_results["Enhanced Filtering"]["details"] += "Testing enhanced filtering capabilities\n"
            
            # Test filtering by category
            if TEST_TOOL.get("category"):
                logger.info(f"Filtering by category: {TEST_TOOL['category']}")
                response = await self.client.get(f"{API_URL}/tools?category={TEST_TOOL['category']}")
                
                if response.status_code != 200:
                    self.test_results["Enhanced Filtering"]["details"] += f"Category filter failed: Status {response.status_code}\n"
                else:
                    tools = response.json()
                    if tools and all(tool.get("category") == TEST_TOOL["category"] for tool in tools):
                        self.test_results["Enhanced Filtering"]["details"] += f"Category filter succeeded: {len(tools)} tools found\n"
                    else:
                        self.test_results["Enhanced Filtering"]["details"] += "Category filter returned incorrect results\n"
            
            # Test filtering by status (published)
            logger.info("Filtering by status: published")
            response = await self.client.get(f"{API_URL}/tools?status=published")
            
            if response.status_code != 200:
                self.test_results["Enhanced Filtering"]["details"] += f"Status filter failed: Status {response.status_code}\n"
            else:
                tools = response.json()
                if tools and all(tool.get("content_status") == "published" for tool in tools):
                    self.test_results["Enhanced Filtering"]["details"] += f"Status filter succeeded: {len(tools)} published tools found\n"
                else:
                    self.test_results["Enhanced Filtering"]["details"] += "Status filter returned incorrect results\n"
            
            # Test filtering by verified status
            logger.info("Filtering by verified status: true")
            response = await self.client.get(f"{API_URL}/tools?verified=true")
            
            if response.status_code != 200:
                self.test_results["Enhanced Filtering"]["details"] += f"Verified filter failed: Status {response.status_code}\n"
            else:
                tools = response.json()
                if tools and all(tool.get("is_verified") == True for tool in tools):
                    self.test_results["Enhanced Filtering"]["details"] += f"Verified filter succeeded: {len(tools)} verified tools found\n"
                else:
                    self.test_results["Enhanced Filtering"]["details"] += "Verified filter returned incorrect results\n"
            
            # Test search functionality
            if self.created_tool_id:
                tool = await self.test_get_ai_tool_by_id()
                if tool and tool.get("name"):
                    search_term = tool["name"].split()[0]  # Use first word of name
                    logger.info(f"Searching for tools with term: {search_term}")
                    response = await self.client.get(f"{API_URL}/tools?search={search_term}")
                    
                    if response.status_code != 200:
                        self.test_results["Enhanced Filtering"]["details"] += f"Search filter failed: Status {response.status_code}\n"
                    else:
                        tools = response.json()
                        if tools and any(search_term.lower() in tool.get("name", "").lower() for tool in tools):
                            self.test_results["Enhanced Filtering"]["details"] += f"Search filter succeeded: {len(tools)} tools found\n"
                        else:
                            self.test_results["Enhanced Filtering"]["details"] += "Search filter returned incorrect results\n"
            
            return True
        except Exception as e:
            error_msg = f"Exception testing enhanced filtering: {str(e)}"
            logger.error(error_msg)
            self.test_results["Enhanced Filtering"]["details"] += f"Enhanced filtering exception: {error_msg}\n"
            return False
            
    def _verify_enhanced_content_generation(self, tool):
        """Verify enhanced content generation fields"""
        # Check for detailed description
        if tool.get("detailed_description"):
            self.test_results["Enhanced Content Generation"]["details"] += f"Detailed description generated: {len(tool['detailed_description'])} chars\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "Detailed description not generated\n"
        
        # Check for structured pricing
        if tool.get("pricing") and isinstance(tool["pricing"], dict):
            pricing = tool["pricing"]
            pricing_details = []
            
            if pricing.get("type"):
                pricing_details.append(f"type: {pricing['type']}")
            
            if pricing.get("starting_price") is not None:
                pricing_details.append(f"starting_price: {pricing['starting_price']}")
            
            if pricing.get("billing_cycle"):
                pricing_details.append(f"billing_cycle: {pricing['billing_cycle']}")
            
            if pricing.get("has_free_plan") is not None:
                pricing_details.append(f"has_free_plan: {pricing['has_free_plan']}")
            
            if pricing.get("tiers") and isinstance(pricing["tiers"], list):
                pricing_details.append(f"tiers: {len(pricing['tiers'])}")
            
            if pricing_details:
                self.test_results["Enhanced Content Generation"]["details"] += f"Structured pricing generated: {', '.join(pricing_details)}\n"
            else:
                self.test_results["Enhanced Content Generation"]["details"] += "Structured pricing incomplete\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "Structured pricing not generated\n"
        
        # Check for FAQs
        if tool.get("faqs") and isinstance(tool["faqs"], list):
            faqs = tool["faqs"]
            if faqs:
                faq_details = []
                for i, faq in enumerate(faqs[:3]):  # Show details for first 3 FAQs
                    if isinstance(faq, dict):
                        faq_details.append(f"FAQ {i+1}: {faq.get('question', 'No question')} ({faq.get('category', 'No category')})")
                
                self.test_results["Enhanced Content Generation"]["details"] += f"FAQs generated: {len(faqs)} items\n"
                if faq_details:
                    self.test_results["Enhanced Content Generation"]["details"] += f"FAQ examples: {'; '.join(faq_details)}\n"
            else:
                self.test_results["Enhanced Content Generation"]["details"] += "FAQs array is empty\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "FAQs not generated\n"
        
        # Check for SEO metadata
        seo_fields = ["meta_title", "meta_description", "meta_keywords"]
        generated_seo = [field for field in seo_fields if tool.get(field)]
        
        if generated_seo:
            self.test_results["Enhanced Content Generation"]["details"] += f"SEO metadata generated: {', '.join(generated_seo)}\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "SEO metadata not generated\n"
        
        # Check for hashtags and haiku
        if tool.get("hashtags") and isinstance(tool["hashtags"], list):
            self.test_results["Enhanced Content Generation"]["details"] += f"Hashtags generated: {len(tool['hashtags'])} items\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "Hashtags not generated\n"
        
        if tool.get("haiku"):
            self.test_results["Enhanced Content Generation"]["details"] += f"Haiku generated: {tool['haiku']}\n"
        else:
            self.test_results["Enhanced Content Generation"]["details"] += "Haiku not generated\n"
    def set_test_results(self):
        """Set the final test results based on the test details"""
        # AI Tool CRUD Operations
        if "Create tool succeeded" in self.test_results["AI Tool CRUD Operations"]["details"] and \
           "Get all tools succeeded" in self.test_results["AI Tool CRUD Operations"]["details"] and \
           "Get tool by ID succeeded" in self.test_results["AI Tool CRUD Operations"]["details"]:
            self.test_results["AI Tool CRUD Operations"]["success"] = True
        
        # OpenAI GPT-4o Integration
        if "OpenAI integration successful" in self.test_results["OpenAI GPT-4o Integration"]["details"]:
            self.test_results["OpenAI GPT-4o Integration"]["success"] = True
        
        # AI Content Generation Engine
        if "Content generation completed successfully" in self.test_results["AI Content Generation Engine"]["details"] and \
           "Generated content:" in self.test_results["AI Content Generation Engine"]["details"]:
            self.test_results["AI Content Generation Engine"]["success"] = True
        
        # Quality Assessment System
        if "Final quality score:" in self.test_results["Quality Assessment System"]["details"] and \
           "Quality score is in valid range" in self.test_results["Quality Assessment System"]["details"]:
            self.test_results["Quality Assessment System"]["success"] = True
        
        # Statistics and Analytics API
        if "Statistics include all expected fields" in self.test_results["Statistics and Analytics API"]["details"]:
            self.test_results["Statistics and Analytics API"]["success"] = True
            
        # Enhanced Tool Creation
        if "All basic info fields correctly stored" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "Logo URL correctly stored" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "Screenshots array correctly stored" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "All social links correctly stored" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "Tags array correctly stored with types and colors" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "Slug auto-generated:" in self.test_results["Enhanced Tool Creation"]["details"] and \
           "Link auto-generated:" in self.test_results["Enhanced Tool Creation"]["details"]:
            self.test_results["Enhanced Tool Creation"]["success"] = True
            
        # Enhanced Content Generation
        if "Detailed description generated:" in self.test_results["Enhanced Content Generation"]["details"] and \
           "Structured pricing generated:" in self.test_results["Enhanced Content Generation"]["details"] and \
           "FAQs generated:" in self.test_results["Enhanced Content Generation"]["details"] and \
           "SEO metadata generated:" in self.test_results["Enhanced Content Generation"]["details"] and \
           "Hashtags generated:" in self.test_results["Enhanced Content Generation"]["details"] and \
           "Haiku generated:" in self.test_results["Enhanced Content Generation"]["details"]:
            self.test_results["Enhanced Content Generation"]["success"] = True
            
        # Slug-based Tool Retrieval
        if "Get tool by slug succeeded:" in self.test_results["Slug-based Tool Retrieval"]["details"] and \
           "Retrieved tool ID matches created tool ID" in self.test_results["Slug-based Tool Retrieval"]["details"]:
            self.test_results["Slug-based Tool Retrieval"]["success"] = True
            
        # Tool Update Endpoint
        if "Update tool succeeded:" in self.test_results["Tool Update Endpoint"]["details"] and \
           "Version incremented to" in self.test_results["Tool Update Endpoint"]["details"]:
            self.test_results["Tool Update Endpoint"]["success"] = True
            
        # Tool Publishing Workflow
        if "Publish tool succeeded:" in self.test_results["Tool Publishing Workflow"]["details"] and \
           "Tool status correctly set to 'published'" in self.test_results["Tool Publishing Workflow"]["details"]:
            self.test_results["Tool Publishing Workflow"]["success"] = True
            
        # Enhanced Filtering
        if "Category filter succeeded:" in self.test_results["Enhanced Filtering"]["details"] or \
           "Status filter succeeded:" in self.test_results["Enhanced Filtering"]["details"] or \
           "Verified filter succeeded:" in self.test_results["Enhanced Filtering"]["details"] or \
           "Search filter succeeded:" in self.test_results["Enhanced Filtering"]["details"]:
            self.test_results["Enhanced Filtering"]["success"] = True
    
    def print_test_summary(self):
        """Print a summary of all test results"""
        print("\n" + "="*80)
        print("DudeAI Backend Test Results Summary")
        print("="*80)
        
        for task, result in self.test_results.items():
            status = "✅ PASSED" if result["success"] else "❌ FAILED"
            print(f"{task}: {status}")
        
        print("\nDetailed Results:")
        print("-"*80)
        
        for task, result in self.test_results.items():
            print(f"\n{task}:")
            print(f"Status: {'PASSED' if result['success'] else 'FAILED'}")
            print("Details:")
            for line in result["details"].strip().split("\n"):
                print(f"  - {line}")
        
        print("\n" + "="*80)

async def run_tests():
    tester = BackendTester()
    
    try:
        # Test AI Tool CRUD Operations
        created_tool = await tester.test_create_ai_tool()
        if created_tool:
            tools = await tester.test_get_ai_tools()
            tool = await tester.test_get_ai_tool_by_id()
            
            # Test slug-based retrieval
            slug_tool = await tester.test_get_ai_tool_by_slug()
            
            # Test AI Content Generation
            if tool:
                generation_result = await tester.test_generate_content()
                if generation_result:
                    # Wait for content generation to complete
                    updated_tool = await tester.wait_for_content_generation()
                    
                    # Verify enhanced content generation
                    if updated_tool:
                        tester._verify_enhanced_content_generation(updated_tool)
                        
                        # Test tool update
                        updated_tool = await tester.test_update_ai_tool()
                        
                        # Test publishing workflow
                        if updated_tool:
                            published = await tester.test_publish_ai_tool()
        
        # Test enhanced filtering
        await tester.test_enhanced_filtering()
        
        # Test Statistics API
        stats = await tester.test_stats_api()
        
        # Set final test results
        tester.set_test_results()
        
        # Print test summary
        tester.print_test_summary()
        
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(run_tests())